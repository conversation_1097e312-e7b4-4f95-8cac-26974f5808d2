/* ==========================================================
 *
 * bootstrap-maxlength.js v 1.7.0
 * Copyright 2015 <PERSON><PERSON><PERSON> @mimonap
 * Licensed under MIT License
 * URL: https://github.com/mimo84/bootstrap-maxlength/blob/master/LICENSE
 *
 * ========================================================== */
!function(e){"use strict";e.event.special.destroyed||(e.event.special.destroyed={remove:function(e){e.handler&&e.handler()}}),e.fn.extend({maxlength:function(t,a){function n(e){var a=e.val();a=t.twoCharLinebreak?a.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"):a.replace(new RegExp("\r?\n","g"),"\n");var n=0;return n=t.utf8?s(a):a.length}function o(e,a){var n=e.val(),o=0;t.twoCharLinebreak&&(n=n.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"),"\n"===n.substr(n.length-1)&&n.length%2===1&&(o=1)),e.val(n.substr(0,a-o))}function s(e){for(var t=0,a=0;a<e.length;a++){var n=e.charCodeAt(a);128>n?t++:t+=n>127&&2048>n?2:3}return t}function r(e,a,o){var s=!0;return!t.alwaysShow&&o-n(e)>a&&(s=!1),s}function i(e,t){var a=t-n(e);return a}function l(e,t){t.css({display:"block"}),e.trigger("maxlength.shown")}function h(e,t){t.css({display:"none"}),e.trigger("maxlength.hidden")}function c(e,a,n){var o="";return t.message?o="function"==typeof t.message?t.message(e,a):t.message.replace("%charsTyped%",n).replace("%charsRemaining%",a-n).replace("%charsTotal%",a):(t.preText&&(o+=t.preText),o+=t.showCharsTyped?n:a-n,t.showMaxLength&&(o+=t.separator+a),t.postText&&(o+=t.postText)),o}function p(e,a,n,o){o&&(o.html(c(a.val(),n,n-e)),e>0?r(a,t.threshold,n)?l(a,o.removeClass(t.limitReachedClass).addClass(t.warningClass)):h(a,o):l(a,o.removeClass(t.warningClass).addClass(t.limitReachedClass))),t.allowOverMax&&(0>e?a.addClass("overmax"):a.removeClass("overmax"))}function f(t){var a=t[0];return e.extend({},"function"==typeof a.getBoundingClientRect?a.getBoundingClientRect():{width:a.offsetWidth,height:a.offsetHeight},t.offset())}function d(a,n){var o=f(a);if("function"===e.type(t.placement))return void t.placement(a,n,o);if(e.isPlainObject(t.placement))return void u(t.placement,n);var s=a.outerWidth(),r=n.outerWidth(),i=n.width(),l=n.height();switch(t.appendToParent&&(o.top-=a.parent().offset().top,o.left-=a.parent().offset().left),t.placement){case"bottom":n.css({top:o.top+o.height,left:o.left+o.width/2-i/2});break;case"top":n.css({top:o.top-l,left:o.left+o.width/2-i/2});break;case"left":n.css({top:o.top+o.height/2-l/2,left:o.left-i});break;case"right":n.css({top:o.top+o.height/2-l/2,left:o.left+o.width});break;case"bottom-right":n.css({top:o.top+o.height,left:o.left+o.width});break;case"top-right":n.css({top:o.top-l,left:o.left+s});break;case"top-left":n.css({top:o.top-l,left:o.left-r});break;case"bottom-left":n.css({top:o.top+a.outerHeight(),left:o.left-r});break;case"centered-right":n.css({top:o.top+l/2,left:o.left+s-r-3});break;case"bottom-right-inside":n.css({top:o.top+o.height,left:o.left+o.width-r});break;case"top-right-inside":n.css({top:o.top-l,left:o.left+s-r});break;case"top-left-inside":n.css({top:o.top-l,left:o.left});break;case"bottom-left-inside":n.css({top:o.top+a.outerHeight(),left:o.left})}}function u(a,n){if(a&&n){var o=["top","bottom","left","right","position"],s={};e.each(o,function(e,a){var n=t.placement[a];"undefined"!=typeof n&&(s[a]=n)}),n.css(s)}}function g(e){var a="maxlength";return t.allowOverMax&&(a="data-bs-mxl"),e.attr(a)||e.attr("size")}var m=e("body"),v={showOnReady:!1,alwaysShow:!1,threshold:10,warningClass:"label label-success",limitReachedClass:"label label-important label-danger",separator:" / ",preText:"",postText:"",showMaxLength:!0,placement:"bottom",message:null,showCharsTyped:!0,validate:!1,utf8:!1,appendToParent:!1,twoCharLinebreak:!0,allowOverMax:!1};return e.isFunction(t)&&!a&&(a=t,t={}),t=e.extend(v,t),this.each(function(){function a(){var a=c(r.val(),n,"0");n=g(r),s||(s=e('<span class="bootstrap-maxlength"></span>').css({display:"none",position:"absolute",whiteSpace:"nowrap",zIndex:1099}).html(a)),r.is("textarea")&&(r.data("maxlenghtsizex",r.outerWidth()),r.data("maxlenghtsizey",r.outerHeight()),r.mouseup(function(){(r.outerWidth()!==r.data("maxlenghtsizex")||r.outerHeight()!==r.data("maxlenghtsizey"))&&d(r,s),r.data("maxlenghtsizex",r.outerWidth()),r.data("maxlenghtsizey",r.outerHeight())})),t.appendToParent?(r.parent().append(s),r.parent().css("position","relative")):m.append(s);var o=i(r,g(r));p(o,r,n,s),d(r,s)}var n,s,r=e(this);e(window).resize(function(){s&&d(r,s)}),t.allowOverMax&&(e(this).attr("data-bs-mxl",e(this).attr("maxlength")),e(this).removeAttr("maxlength")),t.showOnReady?r.ready(function(){a()}):r.focus(function(){a()}),r.on("maxlength.reposition",function(){d(r,s)}),r.on("destroyed",function(){s&&s.remove()}),r.on("blur",function(){s&&!t.showOnReady&&s.remove()}),r.on("input",function(){var e=g(r),a=i(r,e),l=!0;return t.validate&&0>a?(o(r,e),l=!1):p(a,r,n,s),("bottom-right-inside"===t.placement||"top-right-inside"===t.placement)&&d(r,s),l})})}})}(jQuery);