.nav-main-heading {
    color: #FF9D00 !important;
}

.shop-item-text {
    font-weight: 700;
    color: #000
}

.shop-quality-0 {
    color: #000000 !important;
}

.shop-quality-1 {
    color: #000000 !important;
}

.shop-quality-2 {
    color: #69e15e !important;
}

.shop-quality-3 {
    color: #4ccfff !important;
}

.shop-quality-4 {
    color: #f0b71c !important;
}

.shop-quality-5 {
    color: #f08033 !important;
}

.shop-quality-6 {
    color: #8f39ce !important;
}

.shop-quality-7 {
    color: #d9a839 !important;
}

.shop-quality-8 {
    color: #d944ec !important;
}

.shop-quality-9 {
    color: #fe4b4b !important;
}

ul.list-group-horizontal:after {
    clear: both;
    display: block;
    content: "";
}

.flex-fill {
    float: left;
    margin: 20px;
}

ul.list-group-horizontal a {
    color: white;
}

.col-form-label {
    padding-top: calc(.375rem + 1px);
    padding-bottom: calc(.375rem + 1px);
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5
}

.col-form-label-lg {
    padding-top: calc(.5rem + 1px);
    padding-bottom: calc(.5rem + 1px);
    font-size: 1.25rem;
    line-height: 1.5
}

.col-form-label-sm {
    padding-top: calc(.25rem + 1px);
    padding-bottom: calc(.25rem + 1px);
    font-size: .875rem;
    line-height: 1.5
}

.form-control-plaintext {
    display: block;
    width: 100%;
    padding: .375rem 0;
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: #212529;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0
}

.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
    padding-right: 0;
    padding-left: 0
}