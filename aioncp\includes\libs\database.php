<?php

class databaseAdm
{

    public $offline;
    public $error;
    public $errorMessage;
    private $_enableLog = true;
    private $dbs;

    function __construct($server = '', $user = '', $passwd = '', $database = '', $port = '')
    {
        //if (!checks($database))
         //   throw new Exception("Invalid Database Name");
        try {
            $pdo_connect = "sqlsrv:server=" . $server . "," . $port . ";Database=" . $database . "";
            $this->dbs = new \PDO($pdo_connect, $user, $passwd);
            $this->dbs->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
        } catch (\PDOException $e) {
            $this->offline = true;
            $this->errorMessage = "[PDO Exception] " . $e->getmessage();
        }
    }

    public function query($sql, $array = '')
    {
        if (!is_array($array)) $array = array($array);
        $query = $this->dbs->prepare($sql);
        if (!$query) {
            $this->error = true;
            $this->errorMessage = $this->returnError();
            $query->closeCursor();
            return false;
        } else {
            if ($query->execute($array)) {
                $query->closeCursor();
                return true;
            } else {
                $this->error = true;
                $this->errorMessage = $this->returnError($query);
                return false;
            }
        }
    }

    public function queryFetch($sql, $array = '')
    {
        if (!is_array($array))
            $array = array($array);
        $query = $this->dbs->prepare($sql);
        if (!$query) {
            $this->error = true;
            $this->errorMessage = $this->returnError();
            $query->closeCursor();
            return false;
        } else {
            if ($query->execute($array)) {
                $result = $query->fetchAll(PDO::FETCH_ASSOC);
                $query->closeCursor();
                return (checks($result)) ? $result : NULL;
            } else {
                $this->error = true;
                $this->errorMessage = $this->returnError($query);
                return false;
            }
        }
    }

    public function queryFetchSingle($sql, $array = '')
    {
        $result = $this->queryFetch($sql, $array);
        return (isset($result[0])) ? $result[0] : NULL;
    }

    private function returnError($state = '')
    {
        if (!checks($state)) {
            $error = $this->dbs->errorInfo();
        } else {
            $error = $state->errorInfo();
        }
        $errorMessage = '[SQL ' . $error[0] . '] [' . $this->dbs->getAttribute(PDO::ATTR_DRIVER_NAME) . ' ' . $error[1] . '] > ' . $error[2];
        if ($this->_enableLog) {
            @error_log($errorMessage . "\r\n", 3, WEBENGINE_DATABASE_ERRORLOG); //FILE_APPEND);
        }
        return $errorMessage;
    }

    public function lastInsertId()
    {
        return $this->dbs->lastInsertId();
    }
}