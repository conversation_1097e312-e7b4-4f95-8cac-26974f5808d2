<?php

$config['active'] = true;
$config['debug'] = true;

$config['DB_HOST'] = '127.0.0.1';
$config['DB_ACCOUNT'] = 'AionAccounts';
$config['DB_WORLD'] = 'AionWorld_110';
$config['DB_GM'] = 'Aion_log';
$config['DB_WEB_CMS'] = 'aioncms';
$config['DB_USER'] = 'sa';
$config['DB_PASS'] = 'nMHcCAQEEIDojD@+';
$config['DB_PORT'] = '1433';

// AIONCP ACCESS
// 100: admin
// 	90: co-admin
//  80: head gm
//  50: sam
//  30: am

// MODULES
$config['modules'] = array(
    'home' => array(
        '_title' => 'Dashboard',
        '_access' => 1,
        '_separator' => true,
    ),

    'tools' => array(
        '_groupname' => 'AM Tools',
        '_access' => 30,
        '_icon' => 'si-wrench',

        'accountdata' => array(
            '_title' => 'Account Details',
            '_access' => 30,
        ),
        'playerdetails' => array(
            '_title' => 'Player Details',
            '_access' => 30,
        ),
    ),

   'admin' => array(
        '_groupname' => 'Admin Tools',
        '_access' => 1,
        '_icon' => 'si-settings',

        'playereditor' => array(
            '_title' => 'Edit Player\'s',
            '_access' => 90,
            '_hide' => true,
        ),

        'sendmailitems' => array(
            '_title' => 'Send Mail Items to Player',
            '_access' => 90,
        ),

        'setlevel' => array(
            '_title' => 'Set Player\'s Level',
            '_access' => 90,
            '_hide' => true,
        ),

        'setrace' => array(
            '_title' => 'Set Player\'s Race',
            '_access' => 90,
            '_hide' => true,
        ),

        'setclass' => array(
            '_title' => 'Set Player\'s Class',
            '_access' => 90,
            '_hide' => true,
        ),

        'setgatheringlvl' => array(
            '_title' => 'Set Player\'s Gathering Level',
            '_access' => 90,
            '_hide' => true,
        ),

        'setcraftinglvl' => array(
            '_title' => 'Set Player\'s Crafting Level',
            '_access' => 90,
            '_hide' => true,
        ),
        
        'topcredits' => array(
            '_title' => 'View Top Credits',
            '_access' => 90,
        ),

        'paypal' => array(
            '_title' => 'PayPal Logs',
            '_access' => 90,
        ),
        
        'websessions' => array(
            '_title' => 'Web Sessions (live)',
            '_access' => 90,
        ),
    ),

    'webshop' => array(
        '_groupname' => 'Web Shop',
        '_access' => 90,
        '_icon' => 'si-basket',

        'categories' => array(
            '_title' => 'Categories',
            '_access' => 90,
        ),
        'items' => array(
            '_title' => 'Items',
            '_access' => 90,
            //'_hide' => true,
        ),
        'specialshoplist' => array(
            '_title' => 'Weekly Special Items',
            '_access' => 90,
        ),
        'add' => array(
            '_title' => 'Add New Item',
            '_access' => 90,
            '_hide' => true,
        ),
        'edit' => array(
            '_title' => 'Edit Item',
            '_access' => 90,
            '_hide' => true,
        ),
        'logs' => array(
            '_title' => 'Logs',
            '_access' => 90,
        ),
    ),

   /* 'lottery' => array(
        '_groupname' => 'Lottery System',
        '_access' => 90,
        '_icon' => 'si-trophy',

        'status' => array(
            '_title' => 'Status',
            '_access' => 90,
        ),
        'similar' => array(
            '_title' => 'Similar Accounts',
            '_access' => 90,
        ),
        'stash' => array(
            '_title' => 'Stash List',
            '_access' => 90,
        ),
        'list' => array(
            '_title' => 'Lotteries List',
            '_access' => 90,
        ),
    ),*/
);