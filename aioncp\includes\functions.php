<?php
/**
 * AionCMS
 * https://aioncms.com
 *
 * <AUTHOR> <https://lautaroangelico.com/>
 * @copyright (c) 2012-2019 Lautaro Angelico, All Rights Reserved
 */

function checks()
{
    foreach (func_get_args() as $args) {
        if ((@count($args) > 0 and !@empty($args) and @isset($args)) || $args == '0') {
        } else {
            return;
        }
    }
    return true;
}

function debug($value)
{
    echo '<pre>';
    print_r($value);
    echo '</pre>';
}

function sec_to_hms($input_seconds)
{
    if ($input_seconds >= 1) {
        $hours_module = $input_seconds % 3600;
        $hours = ($input_seconds - $hours_module) / 3600;
        $minutes_module = $hours_module % 60;
        $minutes = ($hours_module - $minutes_module) / 60;
        $seconds = $minutes_module;
        return array($hours, $minutes, $seconds);
    } else {
        return array(0, 0, 0);
    }
}

function redirect($location = "")
{
    if (!checks($location)) {
        header('Location: ' . __BASE_URL_ADM__);
        return;
    }
    if (validator::Url($location)) {
        header('Location: ' . $location);
        return;
    }
    header('Location: ' . __BASE_URL_ADM__ . $location);
    return;
}

function config($cfg, $return = true)
{
    global $config;
    if (!checks($cfg)) return;
    if ($return) return $config[$cfg];
    echo $config[$cfg];
}

function filterPost($varname)
{
    return filter_input(INPUT_POST, $varname);
}

function messages($message = "", $type = "")
{
    if (!checks($message)) return;
    switch ($type) {
        case "success":
            echo '<div class="alert alert-success">' . $message . '</div>';
            break;
        case "error":
            echo '<div class="alert alert-danger">' . $message . '</div>';
            break;
        case "warning":
            echo '<div class="alert alert-warning">' . $message . '</div>';
            break;
        case "info":
            echo '<div class="alert alert-info">' . $message . '</div>';
            break;
        default:
            echo '<div class="alert alert-info">' . $message . '</div>';
    }
}

function highRankAccess()
{
    if ($_SESSION['aioncp']['login_flag_web'] >= 50)
        return true;
    return;
}

function numtorace($race) {
    switch ($race) {
        case 1:
            return 'Asmodian';
            break;
        default:
            return 'Elyos';
    }
}

function numtoclass($class) {
    switch ($class) {
        case 0:
            return 'WARRIOR';
            break;
        case 1:
            return 'GLADIATOR';
            break;
        case 2:
            return 'TEMPLAR';
            break;
        case 3:
            return 'SCOUT';
            break;
        case 4:
            return 'ASSASSIN';
            break;
        case 5:
            return 'RANGER';
            break;
        case 6:
            return 'MAGE';
            break; 
        case 7:
            return 'SORCERER';
            break; 
        case 8:
            return 'SPIRIT_MASTER';
            break;
        case 9:
            return 'PRIEST';
            break; 
        case 10:
            return 'CLERIC';
            break;
        case 11:
            return 'CHANTER';
            break;
        case 12:
            return 'TECHNIST';
            break;
        case 13:
            return 'AETHERTECH';
            break;
        case 14:
            return 'GUNSLINGER';
            break;
        case 15:
            return 'MUSE';
            break;
        case 16:
            return 'SONGWEAVER';
            break;          
        default:
            return 'ALL';
    }
}

function getAccountNameFromId($id)
{
    $siels = Handler::loadDB('siel');
    if (!checks($id))
        return;
    $result = $siels->queryFetchSingle("SELECT account FROM web_account WHERE uid = ?", array($id));
    if (is_array($result))
        return $result['name'];
    return;
}

function unstickPlayer($name, $race)
{
    $sdb = Handler::loadDB('sdb');
    if (!checks($name))
        return;
    if (!checks($race))
        return;
    if ($race == 'ELYOS') {
        $data = array(
            '*********',
            '1209.86',
            '1044.74',
            '140.428',
            '0',
            $name
        );
    } else {
        $data = array(
            '*********',
            '571.039',
            '2787.34',
            '299.875',
            '0',
            $name
        );
    }

    $unstick = $sdb->query("UPDATE `players` SET `world_id` = ?, `x` = ?, `y` = ?, `z` = ?, `online` = ? WHERE `name` = ?", $data);
    if (!$unstick) return;

    return true;
}

function banAccount($name)
{
    $dbadm = Handler::loadDB('db');
    if (!checks($name)) return;
    $result = $dbadm->query("UPDATE user_account SET block_flag = 1 WHERE account = ?", array($name));
    if (!$result) return false;
    return true;
}

function unbanAccount($name)
{
    $dbadm = Handler::loadDB('db');
    if (!checks($name)) return;
    $result = $dbadm->query("UPDATE user_account SET block_flag = 0 WHERE account = ?", array($name));
    if (!$result) return false;
    return true;
}

function expToLevel($exp)
{
    if ($exp <= '400') return '1';
    elseif ($exp <= '1433') return '2';
    elseif ($exp <= '3820') return '3';
    elseif ($exp <= '9054') return '4';
    elseif ($exp <= '17655') return '5';
    elseif ($exp <= '30978') return '6';
    elseif ($exp <= '52010') return '7';
    elseif ($exp <= '82982') return '8';
    elseif ($exp <= '126069') return '9';
    elseif ($exp <= '182252') return '10';
    elseif ($exp <= '260622') return '11';
    elseif ($exp <= '360825') return '12';
    elseif ($exp <= '490331') return '13';
    elseif ($exp <= '649169') return '14';
    elseif ($exp <= '844378') return '15';
    elseif ($exp <= '1080479') return '16';
    elseif ($exp <= '1393133') return '17';
    elseif ($exp <= '1793977') return '18';
    elseif ($exp <= '2282186') return '19';
    elseif ($exp <= '2881347') return '20';
    elseif ($exp <= '3659516') return '21';
    elseif ($exp <= '4622407') return '22';
    elseif ($exp <= '5821524') return '23';
    elseif ($exp <= '7227983') return '24';
    elseif ($exp <= '8835056') return '25';
    elseif ($exp <= '10699436') return '26';
    elseif ($exp <= '12853998') return '27';
    elseif ($exp <= '15255815') return '28';
    elseif ($exp <= '18061172') return '29';
    elseif ($exp <= '21551945') return '30';
    elseif ($exp <= '25635643') return '31';
    elseif ($exp <= '30490364') return '32';
    elseif ($exp <= '36299780') return '33';
    elseif ($exp <= '43890745') return '34';
    elseif ($exp <= '53559061') return '35';
    elseif ($exp <= '65392986') return '36';
    elseif ($exp <= '81005799') return '37';
    elseif ($exp <= '98965887') return '38';
    elseif ($exp <= '120006279') return '39';
    elseif ($exp <= '145305546') return '40';
    elseif ($exp <= '174901906') return '41';
    elseif ($exp <= '209678951') return '42';
    elseif ($exp <= '246923912') return '43';
    elseif ($exp <= '286491872') return '44';
    elseif ($exp <= '328812035') return '45';
    elseif ($exp <= '374157438') return '46';
    elseif ($exp <= '422165990') return '47';
    elseif ($exp <= '473102570') return '48';
    elseif ($exp <= '527287631') return '49';
    elseif ($exp <= '584861315') return '50';
    elseif ($exp <= '649149135') return '51';
    elseif ($exp <= '718967268') return '52';
    elseif ($exp <= '793470302') return '53';
    elseif ($exp <= '871508583') return '54';
    elseif ($exp <= '953180528') return '55';
    elseif ($exp <= '1039463797') return '56';
    elseif ($exp <= '1130615342') return '57';
    elseif ($exp <= '1226906283') return '58';
    elseif ($exp <= '1328622680') return '59';
    elseif ($exp <= '1434562107') return '60';
    elseif ($exp <= '1548141590') return '61';
    elseif ($exp <= '1667422949') return '62';
    elseif ($exp <= '1793319043') return '63';
    elseif ($exp <= '1926765410') return '64';
    elseif ($exp <= '2066885620') return '65';
    elseif ($exp <= '2631427378') return '66';
    elseif ($exp <= '4271005600') return '67';
    elseif ($exp <= '8023982311') return '68';
    elseif ($exp <= '15826312699') return '69';
    elseif ($exp <= '31430688278') return '70';
    elseif ($exp <= '62660507393') return '71';
    elseif ($exp <= '117158523579') return '72';
    elseif ($exp <= '212151338979') return '73';
    elseif ($exp <= '374747480973') return '74';
    elseif ($exp <= '608905517112') return '75';
    //elseif ($exp <= '933228334012') return '76';
    else return '75';
}

function generateLegionProfileUrl($id, $name)
{
    $legionName = preg_replace('/\s+/', '-', trim($name));
    $result = __GA_BASE_URL__ . 'legion/' . $id . '/' . $legionName;
    return $result;
}

function checksVersion()
{
    $url = 'http://version.aioncms.com/1.0/index.php';

    $fields = array(
        //'version' => urlencode(__AIONCMS_VERSION__),
        'baseurl' => urlencode(dirname(__BASE_URL_ADM__)),
    );

    foreach ($fields as $key => $value) {
        $fieldsArray[] = $key . '=' . $value;
    }

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, count($fields));
    curl_setopt($ch, CURLOPT_POSTFIELDS, implode("&", $fieldsArray));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'WebEngine');
    curl_setopt($ch, CURLOPT_HEADER, false);
    $result = curl_exec($ch);
    curl_close($ch);

    if (!$result) return;
    $resultArray = json_decode($result, true);
    if (!is_array($resultArray)) return;
    return $resultArray;
}

function numCLassToImg($num)
{
    switch ($num) {
        case '3':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/scout.png" data-toggle="tooltip" data-placement="top" title="Scout" alt="Scout"/>';
            break;
        case '10':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/cleric.png" data-toggle="tooltip" data-placement="top" title="Cleric" alt="Cleric"/>';
            break;
        case '12':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/cleric.png" data-toggle="tooltip" data-placement="top" title="Cleric" alt="Cleric"/>';
            break;
        case '9':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/priest.png" data-toggle="tooltip" data-placement="top" title="Priest" alt="Priest"/>';
            break;
        case '0':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/warrior.png" data-toggle="tooltip" data-placement="top" title="Warrior" alt="Warrior"/>';
            break;
        case '5':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/ranger.png" data-toggle="tooltip" data-placement="top" title="Ranger" alt="Ranger"/>';
            break;
        case '6':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/mage.png" data-toggle="tooltip" data-placement="top" title="Mage" alt="Mage"/>';
            break;
        case '2':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/templar.png" data-toggle="tooltip" data-placement="top" title="Templar" alt="Templar"/>';
            break;
        case '1':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/gladiator.png" data-toggle="tooltip" data-placement="top" title="Gladiator" alt="Gladiator"/>';
            break;
        case '11':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/chanter.png" data-toggle="tooltip" data-placement="top" title="Chanter" alt="Chanter"/>';
            break;
        case '8':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/spiritmaster.png" data-toggle="tooltip" data-placement="top" title="Spirit Master" alt="Spirit Master"/>';
            break;
        case '7':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/sorcerer.png" data-toggle="tooltip" data-placement="top" title="Sorcerer" alt="Sorcerer"/>';
            break;
        case '4':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/assassin.png" data-toggle="tooltip" data-placement="top" title="Assassin" alt="Assassin"/>';
            break;
        case '16':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/bard.png" data-toggle="tooltip" data-placement="top" title="Bard" alt="Bard"/>';
            break;
        case '14':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/gunner.png" data-toggle="tooltip" data-placement="top" title="Gunner" alt="Gunner"/>';
            break;
        case '13':
            return '<img src="' . __GA_BASE_URL__ . 'templates/home/<USER>/character_icons/rider.png" data-toggle="tooltip" data-placement="top" title="Rider" alt="Rider"/>';
            break;
        default:
            return $num;
    }
}

function getItemData($itemId) {
    if (!checks($itemId)) return false;
    $itemInfo = getItemInfo($itemId);
    if (!is_array($itemInfo)) {

        // item information (external database)
        $itemInfoApi = getItemDataFromApi($itemId);
        if (!is_array($itemInfoApi)) return false;
        if (!checks($itemInfoApi['name'])) return false;

        $itemInfo['item_id'] = $itemId;
        $itemInfo['item_name'] = $itemInfoApi['name'];
        $itemInfo['item_quality'] = checks($itemInfoApi['quality']) ? $itemInfoApi['quality'] : '';
        $itemInfo['item_icon'] = checks($itemInfoApi['icon']) ? $itemInfoApi['icon'] : '';

        return $itemInfo;
    }

    return $itemInfo;
}

function getItemDataFromApi($itemId) {
    $apiRequest = file_get_contents(AIONCMS_ITEMS_DATABASE_API . $itemId);
    if (!$apiRequest)
        return false;

    $apiResult = json_decode($apiRequest, true);
    if (!is_array($apiResult))
        return false;
    if (!checks($apiResult['name']))
        return false;

    $itemIcon = checks($apiResult['icon']) ? $apiResult['icon'] : '';
    $itemGrade = checks($apiResult['quality']) ? $apiResult['quality'] : '';

    $aioncmsDatabase = getItemInfo($itemId);
    if (!is_array($aioncmsDatabase)) {
        $db = Handler::loadDB('aioncp');
        $addItem = $db->query("INSERT INTO aion_itemlist (item_id, item_name, item_icon, item_quality) VALUES (?, ?, ?, ?)", array($itemId, $apiResult['name'], $itemIcon, $itemGrade));
    }

    return $apiResult;
}

function getItemInfo($id)
{
    if (!checks($id))
        return false;
    $aioncms = Handler::loadDB('aioncp');
    $itemInfo = $aioncms->queryFetchSingle("SELECT * FROM aion_itemlist WHERE item_id = ?", array($id));
    if (!is_array($itemInfo))
        return false;
    return $itemInfo;
}