﻿!function(o,ea){"object"==typeof module&&"object"==typeof module.exports?module.exports=o.document?ea(o,!0):function(o){if(!o.document)throw Error("jQuery requires a window with a document");return ea(o)}:ea(o)}("undefined"!=typeof window?window:this,function(o,ea){function Ba(a){var b=a.length,d=c.type(a);return"function"===d||c.isWindow(a)?!1:1===a.nodeType&&b?!0:"array"===d||0===b||"number"==typeof b&&0<b&&b-1 in a}function Ca(a,b,d){if(c.isFunction(b))return c.grep(a,function(a,c){return!!b.call(a,
c,a)!==d});if(b.nodeType)return c.grep(a,function(a){return a===b!==d});if("string"==typeof b){if(Yb.test(b))return c.filter(b,a,d);b=c.filter(b,a)}return c.grep(a,function(a){return 0<=c.inArray(a,b)!==d})}function Va(a,b){do a=a[b];while(a&&1!==a.nodeType);return a}function Zb(a){var b=Wa[a]={};return c.each(a.match(H)||[],function(a,c){b[c]=!0}),b}function Xa(){l.addEventListener?(l.removeEventListener("DOMContentLoaded",v,!1),o.removeEventListener("load",v,!1)):(l.detachEvent("onreadystatechange",
v),o.detachEvent("onload",v))}function v(){(l.addEventListener||"load"===event.type||"complete"===l.readyState)&&(Xa(),c.ready())}function Ya(a,b,d){if(void 0===d&&1===a.nodeType){var e="data-"+b.replace($b,"-$1").toLowerCase();if(d=a.getAttribute(e),"string"==typeof d){try{d="true"===d?!0:"false"===d?!1:"null"===d?null:+d+""===d?+d:ac.test(d)?c.parseJSON(d):d}catch(f){}c.data(a,b,d)}else d=void 0}return d}function Da(a){for(var b in a)if(("data"!==b||!c.isEmptyObject(a[b]))&&"toJSON"!==b)return!1;
return!0}function Za(a,b,d,e){if(c.acceptData(a)){var f,g,h=c.expando,i=a.nodeType,j=i?c.cache:a,m=i?a[h]:a[h]&&h;if(m&&j[m]&&(e||j[m].data)||void 0!==d||"string"!=typeof b)return m||(m=i?a[h]=A.pop()||c.guid++:h),j[m]||(j[m]=i?{}:{toJSON:c.noop}),("object"==typeof b||"function"==typeof b)&&(e?j[m]=c.extend(j[m],b):j[m].data=c.extend(j[m].data,b)),g=j[m],e||(g.data||(g.data={}),g=g.data),void 0!==d&&(g[c.camelCase(b)]=d),"string"==typeof b?(f=g[b],null==f&&(f=g[c.camelCase(b)])):f=g,f}}function $a(a,
b,d){if(c.acceptData(a)){var e,f,g=a.nodeType,h=g?c.cache:a,i=g?a[c.expando]:c.expando;if(h[i]){if(b&&(e=d?h[i]:h[i].data)){c.isArray(b)?b=b.concat(c.map(b,c.camelCase)):b in e?b=[b]:(b=c.camelCase(b),b=b in e?[b]:b.split(" "));for(f=b.length;f--;)delete e[b[f]];if(d?!Da(e):!c.isEmptyObject(e))return}(d||(delete h[i].data,Da(h[i])))&&(g?c.cleanData([a],!0):n.deleteExpando||h!=h.window?delete h[i]:h[i]=null)}}}function ka(){return!0}function M(){return!1}function ab(){try{return l.activeElement}catch(a){}}
function bb(a){var b=cb.split("|"),a=a.createDocumentFragment();if(a.createElement)for(;b.length;)a.createElement(b.pop());return a}function s(a,b){var d,e,f=0,g=typeof a.getElementsByTagName!==z?a.getElementsByTagName(b||"*"):typeof a.querySelectorAll!==z?a.querySelectorAll(b||"*"):void 0;if(!g){g=[];for(d=a.childNodes||a;null!=(e=d[f]);f++)!b||c.nodeName(e,b)?g.push(e):c.merge(g,s(e,b))}return void 0===b||b&&c.nodeName(a,b)?c.merge([a],g):g}function bc(a){Ea.test(a.type)&&(a.defaultChecked=a.checked)}
function db(a,b){return c.nodeName(a,"table")&&c.nodeName(11!==b.nodeType?b:b.firstChild,"tr")?a.getElementsByTagName("tbody")[0]||a.appendChild(a.ownerDocument.createElement("tbody")):a}function eb(a){return a.type=(null!==c.find.attr(a,"type"))+"/"+a.type,a}function fb(a){var b=cc.exec(a.type);return b?a.type=b[1]:a.removeAttribute("type"),a}function Fa(a,b){for(var d,e=0;null!=(d=a[e]);e++)c._data(d,"globalEval",!b||c._data(b[e],"globalEval"))}function gb(a,b){if(1===b.nodeType&&c.hasData(a)){var d,
e,f;e=c._data(a);var g=c._data(b,e),h=e.events;if(h)for(d in delete g.handle,g.events={},h){e=0;for(f=h[d].length;f>e;e++)c.event.add(b,d,h[d][e])}g.data&&(g.data=c.extend({},g.data))}}function hb(a,b){var d=c(b.createElement(a)).appendTo(b.body),e=o.getDefaultComputedStyle?o.getDefaultComputedStyle(d[0]).display:c.css(d[0],"display");return d.detach(),e}function ib(a){var b=l,d=jb[a];return d||(d=hb(a,b),"none"!==d&&d||(fa=(fa||c("<iframe frameborder='0' width='0' height='0'/>")).appendTo(b.documentElement),
b=(fa[0].contentWindow||fa[0].contentDocument).document,b.write(),b.close(),d=hb(a,b),fa.detach()),jb[a]=d),d}function kb(a,b){return{get:function(){var d=a();if(null!=d)return d?void delete this.get:(this.get=b).apply(this,arguments)}}}function lb(a,b){if(b in a)return b;for(var d=b.charAt(0).toUpperCase()+b.slice(1),c=b,f=mb.length;f--;)if(b=mb[f]+d,b in a)return b;return c}function nb(a,b){for(var d,e,f,g=[],h=0,i=a.length;i>h;h++)e=a[h],e.style&&(g[h]=c._data(e,"olddisplay"),d=e.style.display,
b?(g[h]||"none"!==d||(e.style.display=""),""===e.style.display&&N(e)&&(g[h]=c._data(e,"olddisplay",ib(e.nodeName)))):g[h]||(f=N(e),(d&&"none"!==d||!f)&&c._data(e,"olddisplay",f?d:c.css(e,"display"))));for(h=0;i>h;h++)e=a[h],e.style&&(b&&"none"!==e.style.display&&""!==e.style.display||(e.style.display=b?g[h]||"":"none"));return a}function ob(a,b,d){return(a=dc.exec(b))?Math.max(0,a[1]-(d||0))+(a[2]||"px"):b}function pb(a,b,d,e,f){for(var b=d===(e?"border":"content")?4:"width"===b?1:0,g=0;4>b;b+=2)"margin"===
d&&(g+=c.css(a,d+T[b],!0,f)),e?("content"===d&&(g-=c.css(a,"padding"+T[b],!0,f)),"margin"!==d&&(g-=c.css(a,"border"+T[b]+"Width",!0,f))):(g+=c.css(a,"padding"+T[b],!0,f),"padding"!==d&&(g+=c.css(a,"border"+T[b]+"Width",!0,f)));return g}function qb(a,b,d){var e=!0,f="width"===b?a.offsetWidth:a.offsetHeight,g=U(a),h=n.boxSizing()&&"border-box"===c.css(a,"boxSizing",!1,g);if(0>=f||null==f){if(f=O(a,b,g),(0>f||null==f)&&(f=a.style[b]),V.test(f))return f;e=h&&(n.boxSizingReliable()||f===a.style[b]);f=
parseFloat(f)||0}return f+pb(a,b,d||(h?"border":"content"),e,g)+"px"}function D(a,b,d,c,f){return new D.prototype.init(a,b,d,c,f)}function rb(){return setTimeout(function(){F=void 0}),F=c.now()}function la(a,b){for(var d,c={height:a},f=0,b=b?1:0;4>f;f+=2-b)d=T[f],c["margin"+d]=c["padding"+d]=a;return b&&(c.opacity=c.width=a),c}function sb(a,b,d){for(var c,f=(ga[b]||[]).concat(ga["*"]),g=0,h=f.length;h>g;g++)if(c=f[g].call(d,b,a))return c}function ec(a,b){var d,e,f,g,h;for(d in a)if(e=c.camelCase(d),
f=b[e],g=a[d],c.isArray(g)&&(f=g[1],g=a[d]=g[0]),d!==e&&(a[e]=g,delete a[d]),h=c.cssHooks[e],h&&"expand"in h)for(d in g=h.expand(g),delete a[e],g)d in a||(a[d]=g[d],b[d]=f);else b[e]=f}function tb(a,b,d){var e,f=0,g=oa.length,h=c.Deferred().always(function(){delete i.elem}),i=function(){if(e)return!1;for(var b=F||rb(),b=Math.max(0,j.startTime+j.duration-b),d=1-(b/j.duration||0),c=0,f=j.tweens.length;f>c;c++)j.tweens[c].run(d);return h.notifyWith(a,[j,d,b]),1>d&&f?b:(h.resolveWith(a,[j]),!1)},j=h.promise({elem:a,
props:c.extend({},b),opts:c.extend(!0,{specialEasing:{}},d),originalProperties:b,originalOptions:d,startTime:F||rb(),duration:d.duration,tweens:[],createTween:function(b,d){var e=c.Tween(a,j.opts,b,d,j.opts.specialEasing[b]||j.opts.easing);return j.tweens.push(e),e},stop:function(b){var d=0,c=b?j.tweens.length:0;if(e)return this;for(e=!0;c>d;d++)j.tweens[d].run(1);return b?h.resolveWith(a,[j,b]):h.rejectWith(a,[j,b]),this}}),d=j.props;for(ec(d,j.opts.specialEasing);g>f;f++)if(b=oa[f].call(j,a,d,j.opts))return b;
return c.map(d,sb,j),c.isFunction(j.opts.start)&&j.opts.start.call(a,j),c.fx.timer(c.extend(i,{elem:a,anim:j,queue:j.opts.queue})),j.progress(j.opts.progress).done(j.opts.done,j.opts.complete).fail(j.opts.fail).always(j.opts.always)}function ub(a){return function(b,d){"string"!=typeof b&&(d=b,b="*");var e,f=0,g=b.toLowerCase().match(H)||[];if(c.isFunction(d))for(;e=g[f++];)"+"===e.charAt(0)?(e=e.slice(1)||"*",(a[e]=a[e]||[]).unshift(d)):(a[e]=a[e]||[]).push(d)}}function vb(a,b,d,e){function f(i){var j;
return g[i]=!0,c.each(a[i]||[],function(a,c){var i=c(b,d,e);return"string"!=typeof i||h||g[i]?h?!(j=i):void 0:(b.dataTypes.unshift(i),f(i),!1)}),j}var g={},h=a===Ga;return f(b.dataTypes[0])||!g["*"]&&f("*")}function Ha(a,b){var d,e,f=c.ajaxSettings.flatOptions||{};for(e in b)void 0!==b[e]&&((f[e]?a:d||(d={}))[e]=b[e]);return d&&c.extend(!0,a,d),a}function Ia(a,b,d,e){var f;if(c.isArray(b))c.each(b,function(b,c){d||fc.test(a)?e(a,c):Ia(a+"["+("object"==typeof c?b:"")+"]",c,d,e)});else if(d||"object"!==
c.type(b))e(a,b);else for(f in b)Ia(a+"["+f+"]",b[f],d,e)}function wb(){try{return new o.XMLHttpRequest}catch(a){}}function xb(a){return c.isWindow(a)?a:9===a.nodeType?a.defaultView||a.parentWindow:!1}var A=[],L=A.slice,yb=A.concat,Ja=A.push,zb=A.indexOf,ra={},gc=ra.toString,W=ra.hasOwnProperty,Ka="".trim,n={},c=function(a,b){return new c.fn.init(a,b)},hc=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ic=/^-ms-/,jc=/-([\da-z])/gi,kc=function(a,b){return b.toUpperCase()};c.fn=c.prototype={jquery:"1.11.0",constructor:c,
selector:"",length:0,toArray:function(){return L.call(this)},get:function(a){return null!=a?0>a?this[a+this.length]:this[a]:L.call(this)},pushStack:function(a){a=c.merge(this.constructor(),a);return a.prevObject=this,a.context=this.context,a},each:function(a,b){return c.each(this,a,b)},map:function(a){return this.pushStack(c.map(this,function(b,d){return a.call(b,d,b)}))},slice:function(){return this.pushStack(L.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},
eq:function(a){var b=this.length,a=+a+(0>a?b:0);return this.pushStack(0<=a&&b>a?[this[a]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:Ja,sort:A.sort,splice:A.splice};c.extend=c.fn.extend=function(){var a,b,d,e,f,g,h=arguments[0]||{},i=1,j=arguments.length,m=!1;"boolean"==typeof h&&(m=h,h=arguments[i]||{},i++);"object"==typeof h||c.isFunction(h)||(h={});for(i===j&&(h=this,i--);j>i;i++)if(null!=(f=arguments[i]))for(e in f)a=h[e],d=f[e],h!==d&&(m&&d&&(c.isPlainObject(d)||
(b=c.isArray(d)))?(b?(b=!1,g=a&&c.isArray(a)?a:[]):g=a&&c.isPlainObject(a)?a:{},h[e]=c.extend(m,g,d)):void 0!==d&&(h[e]=d));return h};c.extend({expando:"jQuery"+("1.11.0"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(a){throw Error(a);},noop:function(){},isFunction:function(a){return"function"===c.type(a)},isArray:Array.isArray||function(a){return"array"===c.type(a)},isWindow:function(a){return null!=a&&a==a.window},isNumeric:function(a){return 0<=a-parseFloat(a)},isEmptyObject:function(a){for(var b in a)return!1;
return!0},isPlainObject:function(a){var b;if(!a||"object"!==c.type(a)||a.nodeType||c.isWindow(a))return!1;try{if(a.constructor&&!W.call(a,"constructor")&&!W.call(a.constructor.prototype,"isPrototypeOf"))return!1}catch(d){return!1}if(n.ownLast)for(b in a)return W.call(a,b);for(b in a);return void 0===b||W.call(a,b)},type:function(a){return null==a?a+"":"object"==typeof a||"function"==typeof a?ra[gc.call(a)]||"object":typeof a},globalEval:function(a){a&&c.trim(a)&&(o.execScript||function(a){o.eval.call(o,
a)})(a)},camelCase:function(a){return a.replace(ic,"ms-").replace(jc,kc)},nodeName:function(a,b){return a.nodeName&&a.nodeName.toLowerCase()===b.toLowerCase()},each:function(a,b,d){var c,f=0,g=a.length,h=Ba(a);if(d)if(h)for(;g>f&&!(c=b.apply(a[f],d),!1===c);f++);else for(f in a){if(c=b.apply(a[f],d),!1===c)break}else if(h)for(;g>f&&!(c=b.call(a[f],f,a[f]),!1===c);f++);else for(f in a)if(c=b.call(a[f],f,a[f]),!1===c)break;return a},trim:Ka&&!Ka.call("﻿ ")?function(a){return null==a?"":Ka.call(a)}:
function(a){return null==a?"":(a+"").replace(hc,"")},makeArray:function(a,b){var d=b||[];return null!=a&&(Ba(Object(a))?c.merge(d,"string"==typeof a?[a]:a):Ja.call(d,a)),d},inArray:function(a,b,d){var c;if(b){if(zb)return zb.call(b,a,d);c=b.length;for(d=d?0>d?Math.max(0,c+d):d:0;c>d;d++)if(d in b&&b[d]===a)return d}return-1},merge:function(a,b){for(var d=+b.length,c=0,f=a.length;d>c;)a[f++]=b[c++];if(d!==d)for(;void 0!==b[c];)a[f++]=b[c++];return a.length=f,a},grep:function(a,b,d){for(var c=[],f=
0,g=a.length,h=!d;g>f;f++)d=!b(a[f],f),d!==h&&c.push(a[f]);return c},map:function(a,b,d){var c,f=0,g=a.length,h=[];if(Ba(a))for(;g>f;f++)c=b(a[f],f,d),null!=c&&h.push(c);else for(f in a)c=b(a[f],f,d),null!=c&&h.push(c);return yb.apply([],h)},guid:1,proxy:function(a,b){var d,e,f;return"string"==typeof b&&(f=a[b],b=a,a=f),c.isFunction(a)?(d=L.call(arguments,2),e=function(){return a.apply(b||this,d.concat(L.call(arguments)))},e.guid=a.guid=a.guid||c.guid++,e):void 0},now:function(){return+new Date},
support:n});c.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(a,b){ra["[object "+b+"]"]=b.toLowerCase()});var ca=function(a){function b(a,b,c,d){var e,f,g,h,i;if((b?b.ownerDocument||b:E)!==K&&X(b),b=b||K,c=c||[],!a||"string"!=typeof a)return c;if(1!==(h=b.nodeType)&&9!==h)return[];if(P&&!d){if(e=ka.exec(a))if(g=e[1])if(9===h){if(f=b.getElementById(g),!f||!f.parentNode)return c;if(f.id===g)return c.push(f),c}else{if(b.ownerDocument&&(f=b.ownerDocument.getElementById(g))&&
w(b,f)&&f.id===g)return c.push(f),c}else{if(e[2])return Y.apply(c,b.getElementsByTagName(a)),c;if((g=e[3])&&p.getElementsByClassName&&b.getElementsByClassName)return Y.apply(c,b.getElementsByClassName(g)),c}if(p.qsa&&(!y||!y.test(a))){if(f=e=C,g=b,i=9===h&&a,1===h&&"object"!==b.nodeName.toLowerCase()){h=u(a);(e=b.getAttribute("id"))?f=e.replace(la,"\\$&"):b.setAttribute("id",f);f="[id='"+f+"'] ";for(g=h.length;g--;)h[g]=f+n(h[g]);g=V.test(a)&&q(b.parentNode)||b;i=h.join(",")}if(i)try{return Y.apply(c,
g.querySelectorAll(i)),c}catch(j){}finally{e||b.removeAttribute("id")}}}var k;a:{var a=a.replace(F,"$1"),m,l;f=u(a);if(!d&&1===f.length){if(k=f[0]=f[0].slice(0),2<k.length&&"ID"===(m=k[0]).type&&p.getById&&9===b.nodeType&&P&&r.relative[k[1].type]){if(b=(r.find.ID(m.matches[0].replace($,aa),b)||[])[0],!b){k=c;break a}a=a.slice(k.shift().value.length)}for(h=N.needsContext.test(a)?0:k.length;h--&&!(m=k[h],r.relative[e=m.type]);)if((l=r.find[e])&&(d=l(m.matches[0].replace($,aa),V.test(k[0].type)&&q(b.parentNode)||
b))){if(k.splice(h,1),a=d.length&&n(k),!a){k=(Y.apply(c,d),c);break a}break}}k=(La(a,f)(d,b,!P,c,V.test(a)&&q(b.parentNode)||b),c)}return k}function d(){function a(c,d){return b.push(c+" ")>r.cacheLength&&delete a[b.shift()],a[c+" "]=d}var b=[];return a}function c(a){return a[C]=!0,a}function f(a){var b=K.createElement("div");try{return!!a(b)}catch(c){return!1}finally{b.parentNode&&b.parentNode.removeChild(b)}}function g(a,b){for(var c=a.split("|"),d=a.length;d--;)r.attrHandle[c[d]]=b}function h(a,
b){var c=b&&a,d=c&&1===a.nodeType&&1===b.nodeType&&(~b.sourceIndex||L)-(~a.sourceIndex||L);if(d)return d;if(c)for(;c=c.nextSibling;)if(c===b)return-1;return a?1:-1}function i(a){return function(b){return"input"===b.nodeName.toLowerCase()&&b.type===a}}function j(a){return function(b){var c=b.nodeName.toLowerCase();return("input"===c||"button"===c)&&b.type===a}}function m(a){return c(function(b){return b=+b,c(function(c,d){for(var e,f=a([],c.length,b),g=f.length;g--;)c[e=f[g]]&&(c[e]=!(d[e]=c[e]))})})}
function q(a){return a&&typeof a.getElementsByTagName!==A&&a}function k(){}function u(a,c){var d,e,f,g,h,i,k;if(h=Ab[a+" "])return c?0:h.slice(0);h=a;i=[];for(k=r.preFilter;h;){(!d||(e=W.exec(h)))&&(e&&(h=h.slice(e[0].length)||h),i.push(f=[]));d=!1;(e=ca.exec(h))&&(d=e.shift(),f.push({value:d,type:e[0].replace(F," ")}),h=h.slice(d.length));for(g in r.filter)!(e=N[g].exec(h))||k[g]&&!(e=k[g](e))||(d=e.shift(),f.push({value:d,type:g,matches:e}),h=h.slice(d.length));if(!d)break}return c?h.length:h?b.error(a):
Ab(a,i).slice(0)}function n(a){for(var b=0,c=a.length,d="";c>b;b++)d+=a[b].value;return d}function l(a,b,c){var d=b.dir,e=c&&"parentNode"===d,f=lc++;return b.first?function(b,c,f){for(;b=b[d];)if(1===b.nodeType||e)return a(b,c,f)}:function(b,c,g){var h,i,k=[I,f];if(g)for(;b=b[d];){if((1===b.nodeType||e)&&a(b,c,g))return!0}else for(;b=b[d];)if(1===b.nodeType||e){if(i=b[C]||(b[C]={}),(h=i[d])&&h[0]===I&&h[1]===f)return k[2]=h[2];if(i[d]=k,k[2]=a(b,c,g))return!0}}}function o(a){return 1<a.length?function(b,
c,d){for(var e=a.length;e--;)if(!a[e](b,c,d))return!1;return!0}:a[0]}function sa(a,b,c,d,e){for(var f,g=[],h=0,i=a.length,k=null!=b;i>h;h++)(f=a[h])&&(!c||c(f,d,e))&&(g.push(f),k&&b.push(h));return g}function s(a,d,f,g,h,i){return g&&!g[C]&&(g=s(g)),h&&!h[C]&&(h=s(h,i)),c(function(c,e,i,k){var j,m,q=[],n=[],u=e.length,l;if(!(l=c)){l=d||"*";for(var J=i.nodeType?[i]:i,p=[],Z=0,o=J.length;o>Z;Z++)b(l,J[Z],p);l=p}l=!a||!c&&d?l:sa(l,q,a,i,k);J=f?h||(c?a:u||g)?[]:e:l;if(f&&f(l,J,i,k),g){j=sa(J,n);g(j,[],
i,k);for(i=j.length;i--;)(m=j[i])&&(J[n[i]]=!(l[n[i]]=m))}if(c){if(h||a){if(h){j=[];for(i=J.length;i--;)(m=J[i])&&j.push(l[i]=m);h(null,J=[],j,k)}for(i=J.length;i--;)(m=J[i])&&-1<(j=h?B.call(c,m):q[i])&&(c[j]=!(e[j]=m))}}else J=sa(J===e?J.splice(u,J.length):J),h?h(null,e,J,k):Y.apply(e,J)})}function D(a){var b,c,d,e=a.length,f=r.relative[a[0].type];c=f||r.relative[" "];for(var g=f?1:0,h=l(function(a){return a===b},c,!0),i=l(function(a){return-1<B.call(b,a)},c,!0),k=[function(a,c,d){return!f&&(d||
c!==ta)||((b=c).nodeType?h(a,c,d):i(a,c,d))}];e>g;g++)if(c=r.relative[a[g].type])k=[l(o(k),c)];else{if(c=r.filter[a[g].type].apply(null,a[g].matches),c[C]){for(d=++g;e>d&&!r.relative[a[d].type];d++);return s(1<g&&o(k),1<g&&n(a.slice(0,g-1).concat({value:" "===a[g-2].type?"*":""})).replace(F,"$1"),c,d>g&&D(a.slice(g,d)),e>d&&D(a=a.slice(d)),e>d&&n(a))}k.push(c)}return o(k)}function mc(a,d){var f=0<d.length,g=0<a.length,h=function(c,e,h,i,k){var j,m,q,n=0,u="0",l=c&&[],ha=[],p=ta,Z=c||g&&r.find.TAG("*",
k),o=I+=null==p?1:Math.random()||0.1,ma=Z.length;for(k&&(ta=e!==K&&e);u!==ma&&null!=(j=Z[u]);u++){if(g&&j){for(m=0;q=a[m++];)if(q(j,e,h)){i.push(j);break}k&&(I=o)}f&&((j=!q&&j)&&n--,c&&l.push(j))}if(n+=u,f&&u!==n){for(m=0;q=d[m++];)q(l,ha,e,h);if(c){if(0<n)for(;u--;)l[u]||ha[u]||(ha[u]=T.call(i));ha=sa(ha)}Y.apply(i,ha);k&&!c&&0<ha.length&&1<n+d.length&&b.uniqueSort(i)}return k&&(I=o,ta=p),l};return f?c(h):h}var t,p,r,ma,Bb,La,ta,ba,na,X,K,G,P,y,ia,ua,w,C="sizzle"+-new Date,E=a.document,I=0,lc=0,
z=d(),Ab=d(),H=d(),v=function(a,b){return a===b&&(na=!0),0},A="undefined",L=-2147483648,Q={}.hasOwnProperty,x=[],T=x.pop,U=x.push,Y=x.push,O=x.slice,B=x.indexOf||function(a){for(var b=0,c=this.length;c>b;b++)if(this[b]===a)return b;return-1},R="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+".replace("w","w#"),S="\\[[\\x20\\t\\r\\n\\f]*((?:\\\\.|[\\w-]|[^\\x00-\\xa0])+)[\\x20\\t\\r\\n\\f]*(?:([*^$|!~]?=)[\\x20\\t\\r\\n\\f]*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+R+")|)|)[\\x20\\t\\r\\n\\f]*\\]",M=":((?:\\\\.|[\\w-]|[^\\x00-\\xa0])+)(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+
S.replace(3,8)+")*)|.*)\\)|)",F=RegExp("^[\\x20\\t\\r\\n\\f]+|((?:^|[^\\\\])(?:\\\\.)*)[\\x20\\t\\r\\n\\f]+$","g"),W=/^[\x20\t\r\n\f]*,[\x20\t\r\n\f]*/,ca=/^[\x20\t\r\n\f]*([>+~]|[\x20\t\r\n\f])[\x20\t\r\n\f]*/,da=RegExp("=[\\x20\\t\\r\\n\\f]*([^\\]'\"]*?)[\\x20\\t\\r\\n\\f]*\\]","g"),ea=RegExp(M),fa=RegExp("^"+R+"$"),N={ID:/^#((?:\\.|[\w-]|[^\x00-\xa0])+)/,CLASS:/^\.((?:\\.|[\w-]|[^\x00-\xa0])+)/,TAG:RegExp("^("+"(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+".replace("w","w*")+")"),ATTR:RegExp("^"+S),PSEUDO:RegExp("^"+
M),CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\([\\x20\\t\\r\\n\\f]*(even|odd|(([+-]|)(\\d*)n|)[\\x20\\t\\r\\n\\f]*(?:([+-]|)[\\x20\\t\\r\\n\\f]*(\\d+)|))[\\x20\\t\\r\\n\\f]*\\)|)","i"),bool:RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:RegExp("^[\\x20\\t\\r\\n\\f]*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\([\\x20\\t\\r\\n\\f]*((?:-\\d)?\\d*)[\\x20\\t\\r\\n\\f]*\\)|)(?=[^-]|$)",
"i")},ga=/^(?:input|select|textarea|button)$/i,ja=/^h\d$/i,qa=/^[^{]+\{\s*\[native \w/,ka=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,V=/[+~]/,la=/'|\\/g,$=RegExp("\\\\([\\da-f]{1,6}[\\x20\\t\\r\\n\\f]?|([\\x20\\t\\r\\n\\f])|.)","ig"),aa=function(a,b,c){a="0x"+b-65536;return a!==a||c?b:0>a?String.fromCharCode(a+65536):String.fromCharCode(a>>10|55296,1023&a|56320)};try{Y.apply(x=O.call(E.childNodes),E.childNodes),x[E.childNodes.length].nodeType}catch(oa){Y={apply:x.length?function(a,b){U.apply(a,O.call(b))}:
function(a,b){for(var c=a.length,d=0;a[c++]=b[d++];);a.length=c-1}}}p=b.support={};Bb=b.isXML=function(a){return(a=a&&(a.ownerDocument||a).documentElement)?"HTML"!==a.nodeName:!1};X=b.setDocument=function(a){var b,c=a?a.ownerDocument||a:E,a=c.defaultView;return c!==K&&9===c.nodeType&&c.documentElement?(K=c,G=c.documentElement,P=!Bb(c),a&&a!==a.top&&(a.addEventListener?a.addEventListener("unload",function(){X()},!1):a.attachEvent&&a.attachEvent("onunload",function(){X()})),p.attributes=f(function(a){return a.className=
"i",!a.getAttribute("className")}),p.getElementsByTagName=f(function(a){return a.appendChild(c.createComment("")),!a.getElementsByTagName("*").length}),p.getElementsByClassName=qa.test(c.getElementsByClassName)&&f(function(a){return a.innerHTML="<div class='a'></div><div class='a i'></div>",a.firstChild.className="i",2===a.getElementsByClassName("i").length}),p.getById=f(function(a){return G.appendChild(a).id=C,!c.getElementsByName||!c.getElementsByName(C).length}),p.getById?(r.find.ID=function(a,
b){if(typeof b.getElementById!==A&&P){var c=b.getElementById(a);return c&&c.parentNode?[c]:[]}},r.filter.ID=function(a){var b=a.replace($,aa);return function(a){return a.getAttribute("id")===b}}):(delete r.find.ID,r.filter.ID=function(a){var b=a.replace($,aa);return function(a){return(a=typeof a.getAttributeNode!==A&&a.getAttributeNode("id"))&&a.value===b}}),r.find.TAG=p.getElementsByTagName?function(a,b){return typeof b.getElementsByTagName!==A?b.getElementsByTagName(a):void 0}:function(a,b){var c,
d=[],e=0,f=b.getElementsByTagName(a);if("*"===a){for(;c=f[e++];)1===c.nodeType&&d.push(c);return d}return f},r.find.CLASS=p.getElementsByClassName&&function(a,b){return typeof b.getElementsByClassName!==A&&P?b.getElementsByClassName(a):void 0},ia=[],y=[],(p.qsa=qa.test(c.querySelectorAll))&&(f(function(a){a.innerHTML="<select t=''><option selected=''></option></select>";a.querySelectorAll("[t^='']").length&&y.push("[*^$]=[\\x20\\t\\r\\n\\f]*(?:''|\"\")");a.querySelectorAll("[selected]").length||y.push("\\[[\\x20\\t\\r\\n\\f]*(?:value|checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)");
a.querySelectorAll(":checked").length||y.push(":checked")}),f(function(a){var b=c.createElement("input");b.setAttribute("type","hidden");a.appendChild(b).setAttribute("name","D");a.querySelectorAll("[name=d]").length&&y.push("name[\\x20\\t\\r\\n\\f]*[*^$|!~]?=");a.querySelectorAll(":enabled").length||y.push(":enabled",":disabled");a.querySelectorAll("*,:x");y.push(",.*:")})),(p.matchesSelector=qa.test(ua=G.webkitMatchesSelector||G.mozMatchesSelector||G.oMatchesSelector||G.msMatchesSelector))&&f(function(a){p.disconnectedMatch=
ua.call(a,"div");ua.call(a,"[s!='']:x");ia.push("!=",M)}),y=y.length&&RegExp(y.join("|")),ia=ia.length&&RegExp(ia.join("|")),b=qa.test(G.compareDocumentPosition),w=b||qa.test(G.contains)?function(a,b){var c=9===a.nodeType?a.documentElement:a,d=b&&b.parentNode;return a===d||!(!d||1!==d.nodeType||!(c.contains?c.contains(d):a.compareDocumentPosition&&16&a.compareDocumentPosition(d)))}:function(a,b){if(b)for(;b=b.parentNode;)if(b===a)return!0;return!1},v=b?function(a,b){if(a===b)return na=!0,0;var d=
!a.compareDocumentPosition-!b.compareDocumentPosition;return d?d:(d=(a.ownerDocument||a)===(b.ownerDocument||b)?a.compareDocumentPosition(b):1,1&d||!p.sortDetached&&b.compareDocumentPosition(a)===d?a===c||a.ownerDocument===E&&w(E,a)?-1:b===c||b.ownerDocument===E&&w(E,b)?1:ba?B.call(ba,a)-B.call(ba,b):0:4&d?-1:1)}:function(a,b){if(a===b)return na=!0,0;var d,e=0;d=a.parentNode;var f=b.parentNode,g=[a],i=[b];if(!d||!f)return a===c?-1:b===c?1:d?-1:f?1:ba?B.call(ba,a)-B.call(ba,b):0;if(d===f)return h(a,
b);for(d=a;d=d.parentNode;)g.unshift(d);for(d=b;d=d.parentNode;)i.unshift(d);for(;g[e]===i[e];)e++;return e?h(g[e],i[e]):g[e]===E?-1:i[e]===E?1:0},c):K};b.matches=function(a,c){return b(a,null,null,c)};b.matchesSelector=function(a,c){if((a.ownerDocument||a)!==K&&X(a),c=c.replace(da,"='$1']"),!(!p.matchesSelector||!P||ia&&ia.test(c)||y&&y.test(c)))try{var d=ua.call(a,c);if(d||p.disconnectedMatch||a.document&&11!==a.document.nodeType)return d}catch(e){}return 0<b(c,K,null,[a]).length};b.contains=function(a,
b){return(a.ownerDocument||a)!==K&&X(a),w(a,b)};b.attr=function(a,b){(a.ownerDocument||a)!==K&&X(a);var c=r.attrHandle[b.toLowerCase()],c=c&&Q.call(r.attrHandle,b.toLowerCase())?c(a,b,!P):void 0;return void 0!==c?c:p.attributes||!P?a.getAttribute(b):(c=a.getAttributeNode(b))&&c.specified?c.value:null};b.error=function(a){throw Error("Syntax error, unrecognized expression: "+a);};b.uniqueSort=function(a){var b,c=[],d=0,e=0;if(na=!p.detectDuplicates,ba=!p.sortStable&&a.slice(0),a.sort(v),na){for(;b=
a[e++];)b===a[e]&&(d=c.push(e));for(;d--;)a.splice(c[d],1)}return ba=null,a};ma=b.getText=function(a){var b,c="",d=0;if(b=a.nodeType)if(1===b||9===b||11===b){if("string"==typeof a.textContent)return a.textContent;for(a=a.firstChild;a;a=a.nextSibling)c+=ma(a)}else{if(3===b||4===b)return a.nodeValue}else for(;b=a[d++];)c+=ma(b);return c};r=b.selectors={cacheLength:50,createPseudo:c,match:N,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",
first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(a){return a[1]=a[1].replace($,aa),a[3]=(a[4]||a[5]||"").replace($,aa),"~="===a[2]&&(a[3]=" "+a[3]+" "),a.slice(0,4)},CHILD:function(a){return a[1]=a[1].toLowerCase(),"nth"===a[1].slice(0,3)?(a[3]||b.error(a[0]),a[4]=+(a[4]?a[5]+(a[6]||1):2*("even"===a[3]||"odd"===a[3])),a[5]=+(a[7]+a[8]||"odd"===a[3])):a[3]&&b.error(a[0]),a},PSEUDO:function(a){var b,c=!a[5]&&a[2];return N.CHILD.test(a[0])?null:(a[3]&&void 0!==a[4]?a[2]=a[4]:c&&ea.test(c)&&
(b=u(c,!0))&&(b=c.indexOf(")",c.length-b)-c.length)&&(a[0]=a[0].slice(0,b),a[2]=c.slice(0,b)),a.slice(0,3))}},filter:{TAG:function(a){var b=a.replace($,aa).toLowerCase();return"*"===a?function(){return!0}:function(a){return a.nodeName&&a.nodeName.toLowerCase()===b}},CLASS:function(a){var b=z[a+" "];return b||(b=RegExp("(^|[\\x20\\t\\r\\n\\f])"+a+"([\\x20\\t\\r\\n\\f]|$)"))&&z(a,function(a){return b.test("string"==typeof a.className&&a.className||typeof a.getAttribute!==A&&a.getAttribute("class")||
"")})},ATTR:function(a,c,d){return function(e){e=b.attr(e,a);return null==e?"!="===c:c?(e+="","="===c?e===d:"!="===c?e!==d:"^="===c?d&&0===e.indexOf(d):"*="===c?d&&-1<e.indexOf(d):"$="===c?d&&e.slice(-d.length)===d:"~="===c?-1<(" "+e+" ").indexOf(d):"|="===c?e===d||e.slice(0,d.length+1)===d+"-":!1):!0}},CHILD:function(a,b,c,d,e){var f="nth"!==a.slice(0,3),g="last"!==a.slice(-4),h="of-type"===b;return 1===d&&0===e?function(a){return!!a.parentNode}:function(b,c,i){var k,j,m,q,n,c=f!==g?"nextSibling":
"previousSibling",u=b.parentNode,l=h&&b.nodeName.toLowerCase(),i=!i&&!h;if(u){if(f){for(;c;){for(j=b;j=j[c];)if(h?j.nodeName.toLowerCase()===l:1===j.nodeType)return!1;n=c="only"===a&&!n&&"nextSibling"}return!0}if(n=[g?u.firstChild:u.lastChild],g&&i){i=u[C]||(u[C]={});k=i[a]||[];q=k[0]===I&&k[1];m=k[0]===I&&k[2];for(j=q&&u.childNodes[q];j=++q&&j&&j[c]||(m=q=0)||n.pop();)if(1===j.nodeType&&++m&&j===b){i[a]=[I,q,m];break}}else if(i&&(k=(b[C]||(b[C]={}))[a])&&k[0]===I)m=k[1];else for(;(j=++q&&j&&j[c]||
(m=q=0)||n.pop())&&(!(h?j.nodeName.toLowerCase()===l:1===j.nodeType)||!++m||!(i&&((j[C]||(j[C]={}))[a]=[I,m]),j===b)););return m-=e,m===d||0===m%d&&0<=m/d}}},PSEUDO:function(a,d){var f,g=r.pseudos[a]||r.setFilters[a.toLowerCase()]||b.error("unsupported pseudo: "+a);return g[C]?g(d):1<g.length?(f=[a,a,"",d],r.setFilters.hasOwnProperty(a.toLowerCase())?c(function(a,b){for(var c,e=g(a,d),f=e.length;f--;)c=B.call(a,e[f]),a[c]=!(b[c]=e[f])}):function(a){return g(a,0,f)}):g}},pseudos:{not:c(function(a){var b=
[],d=[],f=La(a.replace(F,"$1"));return f[C]?c(function(a,b,c,d){for(var e,c=f(a,null,d,[]),d=a.length;d--;)(e=c[d])&&(a[d]=!(b[d]=e))}):function(a,c,e){return b[0]=a,f(b,null,e,d),!d.pop()}}),has:c(function(a){return function(c){return 0<b(a,c).length}}),contains:c(function(a){return function(b){return-1<(b.textContent||b.innerText||ma(b)).indexOf(a)}}),lang:c(function(a){return fa.test(a||"")||b.error("unsupported lang: "+a),a=a.replace($,aa).toLowerCase(),function(b){var c;do if(c=P?b.lang:b.getAttribute("xml:lang")||
b.getAttribute("lang"))return c=c.toLowerCase(),c===a||0===c.indexOf(a+"-");while((b=b.parentNode)&&1===b.nodeType);return!1}}),target:function(b){var c=a.location&&a.location.hash;return c&&c.slice(1)===b.id},root:function(a){return a===G},focus:function(a){return a===K.activeElement&&(!K.hasFocus||K.hasFocus())&&!(!a.type&&!a.href&&!~a.tabIndex)},enabled:function(a){return!1===a.disabled},disabled:function(a){return!0===a.disabled},checked:function(a){var b=a.nodeName.toLowerCase();return"input"===
b&&!!a.checked||"option"===b&&!!a.selected},selected:function(a){return a.parentNode&&a.parentNode.selectedIndex,!0===a.selected},empty:function(a){for(a=a.firstChild;a;a=a.nextSibling)if(6>a.nodeType)return!1;return!0},parent:function(a){return!r.pseudos.empty(a)},header:function(a){return ja.test(a.nodeName)},input:function(a){return ga.test(a.nodeName)},button:function(a){var b=a.nodeName.toLowerCase();return"input"===b&&"button"===a.type||"button"===b},text:function(a){var b;return"input"===a.nodeName.toLowerCase()&&
"text"===a.type&&(null==(b=a.getAttribute("type"))||"text"===b.toLowerCase())},first:m(function(){return[0]}),last:m(function(a,b){return[b-1]}),eq:m(function(a,b,c){return[0>c?c+b:c]}),even:m(function(a,b){for(var c=0;b>c;c+=2)a.push(c);return a}),odd:m(function(a,b){for(var c=1;b>c;c+=2)a.push(c);return a}),lt:m(function(a,b,c){for(b=0>c?c+b:c;0<=--b;)a.push(b);return a}),gt:m(function(a,b,c){for(c=0>c?c+b:c;++c<b;)a.push(c);return a})}};r.pseudos.nth=r.pseudos.eq;for(t in{radio:!0,checkbox:!0,
file:!0,password:!0,image:!0})r.pseudos[t]=i(t);for(t in{submit:!0,reset:!0})r.pseudos[t]=j(t);k.prototype=r.filters=r.pseudos;r.setFilters=new k;La=b.compile=function(a,b){var c,d=[],e=[],f=H[a+" "];if(!f){b||(b=u(a));for(c=b.length;c--;)f=D(b[c]),f[C]?d.push(f):e.push(f);f=H(a,mc(e,d))}return f};return p.sortStable=C.split("").sort(v).join("")===C,p.detectDuplicates=!!na,X(),p.sortDetached=f(function(a){return 1&a.compareDocumentPosition(K.createElement("div"))}),f(function(a){return a.innerHTML=
"<a href='#'></a>","#"===a.firstChild.getAttribute("href")})||g("type|href|height|width",function(a,b,c){return c?void 0:a.getAttribute(b,"type"===b.toLowerCase()?1:2)}),p.attributes&&f(function(a){return a.innerHTML="<input/>",a.firstChild.setAttribute("value",""),""===a.firstChild.getAttribute("value")})||g("value",function(a,b,c){return c||"input"!==a.nodeName.toLowerCase()?void 0:a.defaultValue}),f(function(a){return null==a.getAttribute("disabled")})||g("checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",
function(a,b,c){var d;return c?void 0:!0===a[b]?b.toLowerCase():(d=a.getAttributeNode(b))&&d.specified?d.value:null}),b}(o);c.find=ca;c.expr=ca.selectors;c.expr[":"]=c.expr.pseudos;c.unique=ca.uniqueSort;c.text=ca.getText;c.isXMLDoc=ca.isXML;c.contains=ca.contains;var Cb=c.expr.match.needsContext,Db=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,Yb=/^.[^:#\[\.,]*$/;c.filter=function(a,b,d){var e=b[0];return d&&(a=":not("+a+")"),1===b.length&&1===e.nodeType?c.find.matchesSelector(e,a)?[e]:[]:c.find.matches(a,c.grep(b,
function(a){return 1===a.nodeType}))};c.fn.extend({find:function(a){var b,d=[],e=this,f=e.length;if("string"!=typeof a)return this.pushStack(c(a).filter(function(){for(b=0;f>b;b++)if(c.contains(e[b],this))return!0}));for(b=0;f>b;b++)c.find(a,e[b],d);return d=this.pushStack(1<f?c.unique(d):d),d.selector=this.selector?this.selector+" "+a:a,d},filter:function(a){return this.pushStack(Ca(this,a||[],!1))},not:function(a){return this.pushStack(Ca(this,a||[],!0))},is:function(a){return!!Ca(this,"string"==
typeof a&&Cb.test(a)?c(a):a||[],!1).length}});var ja,l=o.document,nc=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(c.fn.init=function(a,b){var d,e;if(!a)return this;if("string"==typeof a){if(d="<"===a.charAt(0)&&">"===a.charAt(a.length-1)&&3<=a.length?[null,a,null]:nc.exec(a),!d||!d[1]&&b)return!b||b.jquery?(b||ja).find(a):this.constructor(b).find(a);if(d[1]){if(b=b instanceof c?b[0]:b,c.merge(this,c.parseHTML(d[1],b&&b.nodeType?b.ownerDocument||b:l,!0)),Db.test(d[1])&&c.isPlainObject(b))for(d in b)c.isFunction(this[d])?
this[d](b[d]):this.attr(d,b[d]);return this}if(e=l.getElementById(d[2]),e&&e.parentNode){if(e.id!==d[2])return ja.find(a);this.length=1;this[0]=e}return this.context=l,this.selector=a,this}return a.nodeType?(this.context=this[0]=a,this.length=1,this):c.isFunction(a)?"undefined"!=typeof ja.ready?ja.ready(a):a(c):(void 0!==a.selector&&(this.selector=a.selector,this.context=a.context),c.makeArray(a,this))}).prototype=c.fn;ja=c(l);var oc=/^(?:parents|prev(?:Until|All))/,pc={children:!0,contents:!0,next:!0,
prev:!0};c.extend({dir:function(a,b,d){for(var e=[],a=a[b];a&&9!==a.nodeType&&(void 0===d||1!==a.nodeType||!c(a).is(d));)1===a.nodeType&&e.push(a),a=a[b];return e},sibling:function(a,b){for(var c=[];a;a=a.nextSibling)1===a.nodeType&&a!==b&&c.push(a);return c}});c.fn.extend({has:function(a){var b,d=c(a,this),e=d.length;return this.filter(function(){for(b=0;e>b;b++)if(c.contains(this,d[b]))return!0})},closest:function(a,b){for(var d,e=0,f=this.length,g=[],h=Cb.test(a)||"string"!=typeof a?c(a,b||this.context):
0;f>e;e++)for(d=this[e];d&&d!==b;d=d.parentNode)if(11>d.nodeType&&(h?-1<h.index(d):1===d.nodeType&&c.find.matchesSelector(d,a))){g.push(d);break}return this.pushStack(1<g.length?c.unique(g):g)},index:function(a){return a?"string"==typeof a?c.inArray(this[0],c(a)):c.inArray(a.jquery?a[0]:a,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(a,b){return this.pushStack(c.unique(c.merge(this.get(),c(a,b))))},addBack:function(a){return this.add(null==a?this.prevObject:this.prevObject.filter(a))}});
c.each({parent:function(a){return(a=a.parentNode)&&11!==a.nodeType?a:null},parents:function(a){return c.dir(a,"parentNode")},parentsUntil:function(a,b,d){return c.dir(a,"parentNode",d)},next:function(a){return Va(a,"nextSibling")},prev:function(a){return Va(a,"previousSibling")},nextAll:function(a){return c.dir(a,"nextSibling")},prevAll:function(a){return c.dir(a,"previousSibling")},nextUntil:function(a,b,d){return c.dir(a,"nextSibling",d)},prevUntil:function(a,b,d){return c.dir(a,"previousSibling",
d)},siblings:function(a){return c.sibling((a.parentNode||{}).firstChild,a)},children:function(a){return c.sibling(a.firstChild)},contents:function(a){return c.nodeName(a,"iframe")?a.contentDocument||a.contentWindow.document:c.merge([],a.childNodes)}},function(a,b){c.fn[a]=function(d,e){var f=c.map(this,b,d);return"Until"!==a.slice(-5)&&(e=d),e&&"string"==typeof e&&(f=c.filter(e,f)),1<this.length&&(pc[a]||(f=c.unique(f)),oc.test(a)&&(f=f.reverse())),this.pushStack(f)}});var H=/\S+/g,Wa={};c.Callbacks=
function(a){var a="string"==typeof a?Wa[a]||Zb(a):c.extend({},a),b,d,e,f,g,h,i=[],j=!a.once&&[],m=function(c){d=a.memory&&c;e=!0;g=h||0;h=0;f=i.length;for(b=!0;i&&f>g;g++)if(!1===i[g].apply(c[0],c[1])&&a.stopOnFalse){d=!1;break}b=!1;i&&(j?j.length&&m(j.shift()):d?i=[]:q.disable())},q={add:function(){if(i){var e=i.length;!function Z(b){c.each(b,function(b,d){var e=c.type(d);"function"===e?a.unique&&q.has(d)||i.push(d):d&&d.length&&"string"!==e&&Z(d)})}(arguments);b?f=i.length:d&&(h=e,m(d))}return this},
remove:function(){return i&&c.each(arguments,function(a,d){for(var e;-1<(e=c.inArray(d,i,e));)i.splice(e,1),b&&(f>=e&&f--,g>=e&&g--)}),this},has:function(a){return a?-1<c.inArray(a,i):!(!i||!i.length)},empty:function(){return i=[],f=0,this},disable:function(){return i=j=d=void 0,this},disabled:function(){return!i},lock:function(){return j=void 0,d||q.disable(),this},locked:function(){return!j},fireWith:function(a,c){return!i||e&&!j||(c=c||[],c=[a,c.slice?c.slice():c],b?j.push(c):m(c)),this},fire:function(){return q.fireWith(this,
arguments),this},fired:function(){return!!e}};return q};c.extend({Deferred:function(a){var b=[["resolve","done",c.Callbacks("once memory"),"resolved"],["reject","fail",c.Callbacks("once memory"),"rejected"],["notify","progress",c.Callbacks("memory")]],d="pending",e={state:function(){return d},always:function(){return f.done(arguments).fail(arguments),this},then:function(){var a=arguments;return c.Deferred(function(d){c.each(b,function(b,j){var m=c.isFunction(a[b])&&a[b];f[j[1]](function(){var a=m&&
m.apply(this,arguments);a&&c.isFunction(a.promise)?a.promise().done(d.resolve).fail(d.reject).progress(d.notify):d[j[0]+"With"](this===e?d.promise():this,m?[a]:arguments)})});a=null}).promise()},promise:function(a){return null!=a?c.extend(a,e):e}},f={};return e.pipe=e.then,c.each(b,function(a,c){var i=c[2],j=c[3];e[c[1]]=i.add;j&&i.add(function(){d=j},b[1^a][2].disable,b[2][2].lock);f[c[0]]=function(){return f[c[0]+"With"](this===f?e:this,arguments),this};f[c[0]+"With"]=i.fireWith}),e.promise(f),
a&&a.call(f,f),f},when:function(a){var b=0,d=L.call(arguments),e=d.length,f=1!==e||a&&c.isFunction(a.promise)?e:0,g=1===f?a:c.Deferred(),h=function(a,b,c){return function(d){b[a]=this;c[a]=1<arguments.length?L.call(arguments):d;c===i?g.notifyWith(b,c):--f||g.resolveWith(b,c)}},i,j,m;if(1<e){i=Array(e);j=Array(e);for(m=Array(e);e>b;b++)d[b]&&c.isFunction(d[b].promise)?d[b].promise().done(h(b,m,d)).fail(g.reject).progress(h(b,j,i)):--f}return f||g.resolveWith(m,d),g.promise()}});var va;c.fn.ready=function(a){return c.ready.promise().done(a),
this};c.extend({isReady:!1,readyWait:1,holdReady:function(a){a?c.readyWait++:c.ready(!0)},ready:function(a){if(!0===a?!--c.readyWait:!c.isReady){if(!l.body)return setTimeout(c.ready);c.isReady=!0;!0!==a&&0<--c.readyWait||(va.resolveWith(l,[c]),c.fn.trigger&&c(l).trigger("ready").off("ready"))}}});c.ready.promise=function(a){if(!va)if(va=c.Deferred(),"complete"===l.readyState)setTimeout(c.ready);else if(l.addEventListener)l.addEventListener("DOMContentLoaded",v,!1),o.addEventListener("load",v,!1);
else{l.attachEvent("onreadystatechange",v);o.attachEvent("onload",v);var b=!1;try{b=null==o.frameElement&&l.documentElement}catch(d){}b&&b.doScroll&&function f(){if(!c.isReady){try{b.doScroll("left")}catch(a){return setTimeout(f,50)}Xa();c.ready()}}()}return va.promise(a)};var z="undefined",Eb;for(Eb in c(n))break;n.ownLast="0"!==Eb;n.inlineBlockNeedsLayout=!1;c(function(){var a,b,c=l.getElementsByTagName("body")[0];c&&(a=l.createElement("div"),a.style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",
b=l.createElement("div"),c.appendChild(a).appendChild(b),typeof b.style.zoom!==z&&(b.style.cssText="border:0;margin:0;width:1px;padding:1px;display:inline;zoom:1",(n.inlineBlockNeedsLayout=3===b.offsetWidth)&&(c.style.zoom=1)),c.removeChild(a))});(function(){var a=l.createElement("div");if(null==n.deleteExpando){n.deleteExpando=!0;try{delete a.test}catch(b){n.deleteExpando=!1}}})();c.acceptData=function(a){var b=c.noData[(a.nodeName+" ").toLowerCase()],d=+a.nodeType||1;return 1!==d&&9!==d?!1:!b||
!0!==b&&a.getAttribute("classid")===b};var ac=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,$b=/([A-Z])/g;c.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(a){return a=a.nodeType?c.cache[a[c.expando]]:a[c.expando],!!a&&!Da(a)},data:function(a,b,c){return Za(a,b,c)},removeData:function(a,b){return $a(a,b)},_data:function(a,b,c){return Za(a,b,c,!0)},_removeData:function(a,b){return $a(a,b,!0)}});c.fn.extend({data:function(a,b){var d,e,f,g=
this[0],h=g&&g.attributes;if(void 0===a){if(this.length&&(f=c.data(g),1===g.nodeType&&!c._data(g,"parsedAttrs"))){for(d=h.length;d--;)e=h[d].name,0===e.indexOf("data-")&&(e=c.camelCase(e.slice(5)),Ya(g,e,f[e]));c._data(g,"parsedAttrs",!0)}return f}return"object"==typeof a?this.each(function(){c.data(this,a)}):1<arguments.length?this.each(function(){c.data(this,a,b)}):g?Ya(g,a,c.data(g,a)):void 0},removeData:function(a){return this.each(function(){c.removeData(this,a)})}});c.extend({queue:function(a,
b,d){var e;return a?(b=(b||"fx")+"queue",e=c._data(a,b),d&&(!e||c.isArray(d)?e=c._data(a,b,c.makeArray(d)):e.push(d)),e||[]):void 0},dequeue:function(a,b){var b=b||"fx",d=c.queue(a,b),e=d.length,f=d.shift(),g=c._queueHooks(a,b),h=function(){c.dequeue(a,b)};"inprogress"===f&&(f=d.shift(),e--);f&&("fx"===b&&d.unshift("inprogress"),delete g.stop,f.call(a,h,g));!e&&g&&g.empty.fire()},_queueHooks:function(a,b){var d=b+"queueHooks";return c._data(a,d)||c._data(a,d,{empty:c.Callbacks("once memory").add(function(){c._removeData(a,
b+"queue");c._removeData(a,d)})})}});c.fn.extend({queue:function(a,b){var d=2;return"string"!=typeof a&&(b=a,a="fx",d--),arguments.length<d?c.queue(this[0],a):void 0===b?this:this.each(function(){var d=c.queue(this,a,b);c._queueHooks(this,a);"fx"===a&&"inprogress"!==d[0]&&c.dequeue(this,a)})},dequeue:function(a){return this.each(function(){c.dequeue(this,a)})},clearQueue:function(a){return this.queue(a||"fx",[])},promise:function(a,b){var d,e=1,f=c.Deferred(),g=this,h=this.length,i=function(){--e||
f.resolveWith(g,[g])};"string"!=typeof a&&(b=a,a=void 0);for(a=a||"fx";h--;)(d=c._data(g[h],a+"queueHooks"))&&d.empty&&(e++,d.empty.add(i));return i(),f.promise(b)}});var wa=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,T=["Top","Right","Bottom","Left"],N=function(a,b){return a=b||a,"none"===c.css(a,"display")||!c.contains(a.ownerDocument,a)},B=c.access=function(a,b,d,e,f,g,h){var i=0,j=a.length,m=null==d;if("object"===c.type(d))for(i in f=!0,d)c.access(a,b,i,d[i],!0,g,h);else if(void 0!==e&&(f=!0,
c.isFunction(e)||(h=!0),m&&(h?(b.call(a,e),b=null):(m=b,b=function(a,b,d){return m.call(c(a),d)})),b))for(;j>i;i++)b(a[i],d,h?e:e.call(a[i],i,b(a[i],d)));return f?a:m?b.call(a):j?b(a[0],d):g},Ea=/^(?:checkbox|radio)$/i;!function(){var a=l.createDocumentFragment(),b=l.createElement("div"),c=l.createElement("input");if(b.setAttribute("className","t"),b.innerHTML="  <link/><table></table><a href='/a'>a</a>",n.leadingWhitespace=3===b.firstChild.nodeType,n.tbody=!b.getElementsByTagName("tbody").length,
n.htmlSerialize=!!b.getElementsByTagName("link").length,n.html5Clone="<:nav></:nav>"!==l.createElement("nav").cloneNode(!0).outerHTML,c.type="checkbox",c.checked=!0,a.appendChild(c),n.appendChecked=c.checked,b.innerHTML="<textarea>x</textarea>",n.noCloneChecked=!!b.cloneNode(!0).lastChild.defaultValue,a.appendChild(b),b.innerHTML="<input type='radio' checked='checked' name='t'/>",n.checkClone=b.cloneNode(!0).cloneNode(!0).lastChild.checked,n.noCloneEvent=!0,b.attachEvent&&(b.attachEvent("onclick",
function(){n.noCloneEvent=!1}),b.cloneNode(!0).click()),null==n.deleteExpando){n.deleteExpando=!0;try{delete b.test}catch(e){n.deleteExpando=!1}}a=b=c=null}();(function(){var a,b,c=l.createElement("div");for(a in{submit:!0,change:!0,focusin:!0})b="on"+a,(n[a+"Bubbles"]=b in o)||(c.setAttribute(b,"t"),n[a+"Bubbles"]=!1===c.attributes[b].expando)})();var Ma=/^(?:input|select|textarea)$/i,qc=/^key/,rc=/^(?:mouse|contextmenu)|click/,Fb=/^(?:focusinfocus|focusoutblur)$/,Gb=/^([^.]*)(?:\.(.+)|)$/;c.event=
{global:{},add:function(a,b,d,e,f){var g,h,i,j,m,q,k,n,l,o;if(i=c._data(a)){d.handler&&(j=d,d=j.handler,f=j.selector);d.guid||(d.guid=c.guid++);(h=i.events)||(h=i.events={});(q=i.handle)||(q=i.handle=function(a){return typeof c===z||a&&c.event.triggered===a.type?void 0:c.event.dispatch.apply(q.elem,arguments)},q.elem=a);b=(b||"").match(H)||[""];for(i=b.length;i--;)g=Gb.exec(b[i])||[],l=o=g[1],g=(g[2]||"").split(".").sort(),l&&(m=c.event.special[l]||{},l=(f?m.delegateType:m.bindType)||l,m=c.event.special[l]||
{},k=c.extend({type:l,origType:o,data:e,handler:d,guid:d.guid,selector:f,needsContext:f&&c.expr.match.needsContext.test(f),namespace:g.join(".")},j),(n=h[l])||(n=h[l]=[],n.delegateCount=0,m.setup&&!1!==m.setup.call(a,e,g,q)||(a.addEventListener?a.addEventListener(l,q,!1):a.attachEvent&&a.attachEvent("on"+l,q))),m.add&&(m.add.call(a,k),k.handler.guid||(k.handler.guid=d.guid)),f?n.splice(n.delegateCount++,0,k):n.push(k),c.event.global[l]=!0);a=null}},remove:function(a,b,d,e,f){var g,h,i,j,m,q,k,n,l,
o,s,t=c.hasData(a)&&c._data(a);if(t&&(q=t.events)){b=(b||"").match(H)||[""];for(m=b.length;m--;)if(i=Gb.exec(b[m])||[],l=s=i[1],o=(i[2]||"").split(".").sort(),l){k=c.event.special[l]||{};l=(e?k.delegateType:k.bindType)||l;n=q[l]||[];i=i[2]&&RegExp("(^|\\.)"+o.join("\\.(?:.*\\.|)")+"(\\.|$)");for(j=g=n.length;g--;)h=n[g],!f&&s!==h.origType||d&&d.guid!==h.guid||i&&!i.test(h.namespace)||e&&e!==h.selector&&("**"!==e||!h.selector)||(n.splice(g,1),h.selector&&n.delegateCount--,k.remove&&k.remove.call(a,
h));j&&!n.length&&(k.teardown&&!1!==k.teardown.call(a,o,t.handle)||c.removeEvent(a,l,t.handle),delete q[l])}else for(l in q)c.event.remove(a,l+b[m],d,e,!0);c.isEmptyObject(q)&&(delete t.handle,c._removeData(a,"events"))}},trigger:function(a,b,d,e){var f,g,h,i,j,m,n=[d||l],k=W.call(a,"type")?a.type:a;m=W.call(a,"namespace")?a.namespace.split("."):[];if(h=f=d=d||l,3!==d.nodeType&&8!==d.nodeType&&!Fb.test(k+c.event.triggered)&&(0<=k.indexOf(".")&&(m=k.split("."),k=m.shift(),m.sort()),g=0>k.indexOf(":")&&
"on"+k,a=a[c.expando]?a:new c.Event(k,"object"==typeof a&&a),a.isTrigger=e?2:3,a.namespace=m.join("."),a.namespace_re=a.namespace?RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,a.result=void 0,a.target||(a.target=d),b=null==b?[a]:c.makeArray(b,[a]),j=c.event.special[k]||{},e||!j.trigger||!1!==j.trigger.apply(d,b))){if(!e&&!j.noBubble&&!c.isWindow(d)){i=j.delegateType||k;for(Fb.test(i+k)||(h=h.parentNode);h;h=h.parentNode)n.push(h),f=h;f===(d.ownerDocument||l)&&n.push(f.defaultView||f.parentWindow||
o)}for(m=0;(h=n[m++])&&!a.isPropagationStopped();)a.type=1<m?i:j.bindType||k,(f=(c._data(h,"events")||{})[a.type]&&c._data(h,"handle"))&&f.apply(h,b),(f=g&&h[g])&&f.apply&&c.acceptData(h)&&(a.result=f.apply(h,b),!1===a.result&&a.preventDefault());if(a.type=k,!e&&!a.isDefaultPrevented()&&(!j._default||!1===j._default.apply(n.pop(),b))&&c.acceptData(d)&&g&&d[k]&&!c.isWindow(d)){(f=d[g])&&(d[g]=null);c.event.triggered=k;try{d[k]()}catch(u){}c.event.triggered=void 0;f&&(d[g]=f)}return a.result}},dispatch:function(a){var a=
c.event.fix(a),b,d,e,f,g,h=[],i=L.call(arguments);b=(c._data(this,"events")||{})[a.type]||[];var j=c.event.special[a.type]||{};if(i[0]=a,a.delegateTarget=this,!j.preDispatch||!1!==j.preDispatch.call(this,a)){h=c.event.handlers.call(this,a,b);for(b=0;(f=h[b++])&&!a.isPropagationStopped();){a.currentTarget=f.elem;for(g=0;(e=f.handlers[g++])&&!a.isImmediatePropagationStopped();)(!a.namespace_re||a.namespace_re.test(e.namespace))&&(a.handleObj=e,a.data=e.data,d=((c.event.special[e.origType]||{}).handle||
e.handler).apply(f.elem,i),void 0!==d&&!1===(a.result=d)&&(a.preventDefault(),a.stopPropagation()))}return j.postDispatch&&j.postDispatch.call(this,a),a.result}},handlers:function(a,b){var d,e,f,g,h=[],i=b.delegateCount,j=a.target;if(i&&j.nodeType&&(!a.button||"click"!==a.type))for(;j!=this;j=j.parentNode||this)if(1===j.nodeType&&(!0!==j.disabled||"click"!==a.type)){f=[];for(g=0;i>g;g++)e=b[g],d=e.selector+" ",void 0===f[d]&&(f[d]=e.needsContext?0<=c(d,this).index(j):c.find(d,this,null,[j]).length),
f[d]&&f.push(e);f.length&&h.push({elem:j,handlers:f})}return i<b.length&&h.push({elem:this,handlers:b.slice(i)}),h},fix:function(a){if(a[c.expando])return a;var b,d,e;b=a.type;var f=a,g=this.fixHooks[b];g||(this.fixHooks[b]=g=rc.test(b)?this.mouseHooks:qc.test(b)?this.keyHooks:{});e=g.props?this.props.concat(g.props):this.props;a=new c.Event(f);for(b=e.length;b--;)d=e[b],a[d]=f[d];return a.target||(a.target=f.srcElement||l),3===a.target.nodeType&&(a.target=a.target.parentNode),a.metaKey=!!a.metaKey,
g.filter?g.filter(a,f):a},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:["char","charCode","key","keyCode"],filter:function(a,b){return null==a.which&&(a.which=null!=b.charCode?b.charCode:b.keyCode),a}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(a,b){var c,e,f,g=b.button,h=b.fromElement;
return null==a.pageX&&null!=b.clientX&&(e=a.target.ownerDocument||l,f=e.documentElement,c=e.body,a.pageX=b.clientX+(f&&f.scrollLeft||c&&c.scrollLeft||0)-(f&&f.clientLeft||c&&c.clientLeft||0),a.pageY=b.clientY+(f&&f.scrollTop||c&&c.scrollTop||0)-(f&&f.clientTop||c&&c.clientTop||0)),!a.relatedTarget&&h&&(a.relatedTarget=h===a.target?b.toElement:h),a.which||void 0===g||(a.which=1&g?1:2&g?3:4&g?2:0),a}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==ab()&&this.focus)try{return this.focus(),
!1}catch(a){}},delegateType:"focusin"},blur:{trigger:function(){return this===ab()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return c.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(a){return c.nodeName(a.target,"a")}},beforeunload:{postDispatch:function(a){void 0!==a.result&&(a.originalEvent.returnValue=a.result)}}},simulate:function(a,b,d,e){a=c.extend(new c.Event,d,{type:a,isSimulated:!0,originalEvent:{}});
e?c.event.trigger(a,null,b):c.event.dispatch.call(b,a);a.isDefaultPrevented()&&d.preventDefault()}};c.removeEvent=l.removeEventListener?function(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}:function(a,b,c){b="on"+b;a.detachEvent&&(typeof a[b]===z&&(a[b]=null),a.detachEvent(b,c))};c.Event=function(a,b){return this instanceof c.Event?(a&&a.type?(this.originalEvent=a,this.type=a.type,this.isDefaultPrevented=a.defaultPrevented||void 0===a.defaultPrevented&&(!1===a.returnValue||a.getPreventDefault&&
a.getPreventDefault())?ka:M):this.type=a,b&&c.extend(this,b),this.timeStamp=a&&a.timeStamp||c.now(),void(this[c.expando]=!0)):new c.Event(a,b)};c.Event.prototype={isDefaultPrevented:M,isPropagationStopped:M,isImmediatePropagationStopped:M,preventDefault:function(){var a=this.originalEvent;this.isDefaultPrevented=ka;a&&(a.preventDefault?a.preventDefault():a.returnValue=!1)},stopPropagation:function(){var a=this.originalEvent;this.isPropagationStopped=ka;a&&(a.stopPropagation&&a.stopPropagation(),a.cancelBubble=
!0)},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=ka;this.stopPropagation()}};c.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(a,b){c.event.special[a]={delegateType:b,bindType:b,handle:function(a){var e,f=a.relatedTarget,g=a.handleObj;return(!f||f!==this&&!c.contains(this,f))&&(a.type=g.origType,e=g.handler.apply(this,arguments),a.type=b),e}}});n.submitBubbles||(c.event.special.submit={setup:function(){return c.nodeName(this,"form")?!1:void c.event.add(this,
"click._submit keypress._submit",function(a){a=a.target;(a=c.nodeName(a,"input")||c.nodeName(a,"button")?a.form:void 0)&&!c._data(a,"submitBubbles")&&(c.event.add(a,"submit._submit",function(a){a._submit_bubble=true}),c._data(a,"submitBubbles",true))})},postDispatch:function(a){a._submit_bubble&&(delete a._submit_bubble,this.parentNode&&!a.isTrigger&&c.event.simulate("submit",this.parentNode,a,!0))},teardown:function(){return c.nodeName(this,"form")?!1:void c.event.remove(this,"._submit")}});n.changeBubbles||
(c.event.special.change={setup:function(){return Ma.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(c.event.add(this,"propertychange._change",function(a){"checked"===a.originalEvent.propertyName&&(this._just_changed=!0)}),c.event.add(this,"click._change",function(a){this._just_changed&&!a.isTrigger&&(this._just_changed=!1);c.event.simulate("change",this,a,!0)})),!1):void c.event.add(this,"beforeactivate._change",function(a){a=a.target;Ma.test(a.nodeName)&&!c._data(a,"changeBubbles")&&
(c.event.add(a,"change._change",function(a){!this.parentNode||a.isSimulated||a.isTrigger||c.event.simulate("change",this.parentNode,a,!0)}),c._data(a,"changeBubbles",!0))})},handle:function(a){var b=a.target;return this!==b||a.isSimulated||a.isTrigger||"radio"!==b.type&&"checkbox"!==b.type?a.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return c.event.remove(this,"._change"),!Ma.test(this.nodeName)}});n.focusinBubbles||c.each({focus:"focusin",blur:"focusout"},function(a,b){var d=
function(a){c.event.simulate(b,a.target,c.event.fix(a),!0)};c.event.special[b]={setup:function(){var e=this.ownerDocument||this,f=c._data(e,b);f||e.addEventListener(a,d,!0);c._data(e,b,(f||0)+1)},teardown:function(){var e=this.ownerDocument||this,f=c._data(e,b)-1;f?c._data(e,b,f):(e.removeEventListener(a,d,!0),c._removeData(e,b))}}});c.fn.extend({on:function(a,b,d,e,f){var g,h;if("object"==typeof a){"string"!=typeof b&&(d=d||b,b=void 0);for(g in a)this.on(g,b,d,a[g],f);return this}if(null==d&&null==
e?(e=b,d=b=void 0):null==e&&("string"==typeof b?(e=d,d=void 0):(e=d,d=b,b=void 0)),!1===e)e=M;else if(!e)return this;return 1===f&&(h=e,e=function(a){return c().off(a),h.apply(this,arguments)},e.guid=h.guid||(h.guid=c.guid++)),this.each(function(){c.event.add(this,a,e,d,b)})},one:function(a,b,c,e){return this.on(a,b,c,e,1)},off:function(a,b,d){var e,f;if(a&&a.preventDefault&&a.handleObj)return e=a.handleObj,c(a.delegateTarget).off(e.namespace?e.origType+"."+e.namespace:e.origType,e.selector,e.handler),
this;if("object"==typeof a){for(f in a)this.off(f,b,a[f]);return this}return(!1===b||"function"==typeof b)&&(d=b,b=void 0),!1===d&&(d=M),this.each(function(){c.event.remove(this,a,d,b)})},trigger:function(a,b){return this.each(function(){c.event.trigger(a,b,this)})},triggerHandler:function(a,b){var d=this[0];return d?c.event.trigger(a,b,d,!0):void 0}});var cb="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",
sc=/ jQuery\d+="(?:null|\d+)"/g,Hb=RegExp("<(?:"+cb+")[\\s/>]","i"),Na=/^\s+/,Ib=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Jb=/<([\w:]+)/,Kb=/<tbody/i,tc=/<|&#?\w+;/,uc=/<(?:script|style|link)/i,vc=/checked\s*(?:[^=]|=\s*.checked.)/i,Lb=/^$|\/(?:java|ecma)script/i,cc=/^true\/(.*)/,wc=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,t={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>",
"</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:n.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Oa=bb(l).appendChild(l.createElement("div"));t.optgroup=t.option;t.tbody=t.tfoot=t.colgroup=t.caption=t.thead;t.th=t.td;c.extend({clone:function(a,b,d){var e,f,g,h,i,j=c.contains(a.ownerDocument,a);if(n.html5Clone||c.isXMLDoc(a)||!Hb.test("<"+
a.nodeName+">")?g=a.cloneNode(!0):(Oa.innerHTML=a.outerHTML,Oa.removeChild(g=Oa.firstChild)),!(n.noCloneEvent&&n.noCloneChecked||1!==a.nodeType&&11!==a.nodeType||c.isXMLDoc(a))){e=s(g);i=s(a);for(h=0;null!=(f=i[h]);++h)if(e[h]){var m=e[h],l=void 0,k=void 0,u=void 0;if(1===m.nodeType){if(l=m.nodeName.toLowerCase(),!n.noCloneEvent&&m[c.expando]){u=c._data(m);for(k in u.events)c.removeEvent(m,k,u.handle);m.removeAttribute(c.expando)}"script"===l&&m.text!==f.text?(eb(m).text=f.text,fb(m)):"object"===
l?(m.parentNode&&(m.outerHTML=f.outerHTML),n.html5Clone&&f.innerHTML&&!c.trim(m.innerHTML)&&(m.innerHTML=f.innerHTML)):"input"===l&&Ea.test(f.type)?(m.defaultChecked=m.checked=f.checked,m.value!==f.value&&(m.value=f.value)):"option"===l?m.defaultSelected=m.selected=f.defaultSelected:("input"===l||"textarea"===l)&&(m.defaultValue=f.defaultValue)}}}if(b)if(d){i=i||s(a);e=e||s(g);for(h=0;null!=(f=i[h]);h++)gb(f,e[h])}else gb(a,g);return e=s(g,"script"),0<e.length&&Fa(e,!j&&s(a,"script")),g},buildFragment:function(a,
b,d,e){for(var f,g,h,i,j,m,l,k=a.length,u=bb(b),o=[],pa=0;k>pa;pa++)if(g=a[pa],g||0===g)if("object"===c.type(g))c.merge(o,g.nodeType?[g]:g);else if(tc.test(g)){i=i||u.appendChild(b.createElement("div"));j=(Jb.exec(g)||["",""])[1].toLowerCase();l=t[j]||t._default;i.innerHTML=l[1]+g.replace(Ib,"<$1></$2>")+l[2];for(f=l[0];f--;)i=i.lastChild;if(!n.leadingWhitespace&&Na.test(g)&&o.push(b.createTextNode(Na.exec(g)[0])),!n.tbody)for(f=(g="table"!==j||Kb.test(g)?"<table>"!==l[1]||Kb.test(g)?0:i:i.firstChild)&&
g.childNodes.length;f--;)c.nodeName(m=g.childNodes[f],"tbody")&&!m.childNodes.length&&g.removeChild(m);c.merge(o,i.childNodes);for(i.textContent="";i.firstChild;)i.removeChild(i.firstChild);i=u.lastChild}else o.push(b.createTextNode(g));i&&u.removeChild(i);n.appendChecked||c.grep(s(o,"input"),bc);for(pa=0;g=o[pa++];)if((!e||-1===c.inArray(g,e))&&(h=c.contains(g.ownerDocument,g),i=s(u.appendChild(g),"script"),h&&Fa(i),d))for(f=0;g=i[f++];)Lb.test(g.type||"")&&d.push(g);return u},cleanData:function(a,
b){for(var d,e,f,g,h=0,i=c.expando,j=c.cache,m=n.deleteExpando,l=c.event.special;null!=(d=a[h]);h++)if((b||c.acceptData(d))&&(f=d[i],g=f&&j[f])){if(g.events)for(e in g.events)l[e]?c.event.remove(d,e):c.removeEvent(d,e,g.handle);j[f]&&(delete j[f],m?delete d[i]:typeof d.removeAttribute!==z?d.removeAttribute(i):d[i]=null,A.push(f))}}});c.fn.extend({text:function(a){return B(this,function(a){return void 0===a?c.text(this):this.empty().append((this[0]&&this[0].ownerDocument||l).createTextNode(a))},null,
a,arguments.length)},append:function(){return this.domManip(arguments,function(a){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&db(this,a).appendChild(a)})},prepend:function(){return this.domManip(arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var b=db(this,a);b.insertBefore(a,b.firstChild)}})},before:function(){return this.domManip(arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this)})},after:function(){return this.domManip(arguments,
function(a){this.parentNode&&this.parentNode.insertBefore(a,this.nextSibling)})},remove:function(a,b){for(var d,e=a?c.filter(a,this):this,f=0;null!=(d=e[f]);f++)b||1!==d.nodeType||c.cleanData(s(d)),d.parentNode&&(b&&c.contains(d.ownerDocument,d)&&Fa(s(d,"script")),d.parentNode.removeChild(d));return this},empty:function(){for(var a,b=0;null!=(a=this[b]);b++){for(1===a.nodeType&&c.cleanData(s(a,!1));a.firstChild;)a.removeChild(a.firstChild);a.options&&c.nodeName(a,"select")&&(a.options.length=0)}return this},
clone:function(a,b){return a=null==a?!1:a,b=null==b?a:b,this.map(function(){return c.clone(this,a,b)})},html:function(a){return B(this,function(a){var d=this[0]||{},e=0,f=this.length;if(void 0===a)return 1===d.nodeType?d.innerHTML.replace(sc,""):void 0;if(!("string"!=typeof a||uc.test(a)||!n.htmlSerialize&&Hb.test(a)||!n.leadingWhitespace&&Na.test(a)||t[(Jb.exec(a)||["",""])[1].toLowerCase()])){a=a.replace(Ib,"<$1></$2>");try{for(;f>e;e++)d=this[e]||{},1===d.nodeType&&(c.cleanData(s(d,!1)),d.innerHTML=
a);d=0}catch(g){}}d&&this.empty().append(a)},null,a,arguments.length)},replaceWith:function(){var a=arguments[0];return this.domManip(arguments,function(b){a=this.parentNode;c.cleanData(s(this));a&&a.replaceChild(b,this)}),a&&(a.length||a.nodeType)?this:this.remove()},detach:function(a){return this.remove(a,!0)},domManip:function(a,b){var a=yb.apply([],a),d,e,f,g,h=0,i=this.length,j=this,m=i-1,l=a[0],k=c.isFunction(l);if(k||1<i&&"string"==typeof l&&!n.checkClone&&vc.test(l))return this.each(function(c){var d=
j.eq(c);k&&(a[0]=l.call(this,c,d.html()));d.domManip(a,b)});if(i&&(g=c.buildFragment(a,this[0].ownerDocument,!1,this),d=g.firstChild,1===g.childNodes.length&&(g=d),d)){f=c.map(s(g,"script"),eb);for(e=f.length;i>h;h++)d=g,h!==m&&(d=c.clone(d,!0,!0),e&&c.merge(f,s(d,"script"))),b.call(this[h],d,h);if(e){g=f[f.length-1].ownerDocument;c.map(f,fb);for(h=0;e>h;h++)d=f[h],Lb.test(d.type||"")&&!c._data(d,"globalEval")&&c.contains(g,d)&&(d.src?c._evalUrl&&c._evalUrl(d.src):c.globalEval((d.text||d.textContent||
d.innerHTML||"").replace(wc,"")))}g=d=null}return this}});c.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(a,b){c.fn[a]=function(a){for(var e=0,f=[],g=c(a),h=g.length-1;h>=e;e++)a=e===h?this:this.clone(!0),c(g[e])[b](a),Ja.apply(f,a.get());return this.pushStack(f)}});var fa,jb={};!function(){var a,b,c=l.createElement("div");c.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";a=c.getElementsByTagName("a")[0];
a.style.cssText="float:left;opacity:.5";n.opacity=/^0.5/.test(a.style.opacity);n.cssFloat=!!a.style.cssFloat;c.style.backgroundClip="content-box";c.cloneNode(!0).style.backgroundClip="";n.clearCloneStyle="content-box"===c.style.backgroundClip;a=c=null;n.shrinkWrapBlocks=function(){var a,c,d;if(null==b){if(a=l.getElementsByTagName("body")[0],!a)return;c=l.createElement("div");d=l.createElement("div");a.appendChild(c).appendChild(d);b=!1;typeof d.style.zoom!==z&&(d.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;padding:0;margin:0;border:0;width:1px;padding:1px;zoom:1",
d.innerHTML="<div></div>",d.firstChild.style.width="5px",b=3!==d.offsetWidth);a.removeChild(c)}return b}}();var Mb=/^margin/,V=RegExp("^("+wa+")(?!px)[a-z%]+$","i"),U,O,xc=/^(top|right|bottom|left)$/;o.getComputedStyle?(U=function(a){return a.ownerDocument.defaultView.getComputedStyle(a,null)},O=function(a,b,d){var e,f,g,h,i=a.style;return d=d||U(a),h=d?d.getPropertyValue(b)||d[b]:void 0,d&&(""!==h||c.contains(a.ownerDocument,a)||(h=c.style(a,b)),V.test(h)&&Mb.test(b)&&(e=i.width,f=i.minWidth,g=i.maxWidth,
i.minWidth=i.maxWidth=i.width=h,h=d.width,i.width=e,i.minWidth=f,i.maxWidth=g)),void 0===h?h:h+""}):l.documentElement.currentStyle&&(U=function(a){return a.currentStyle},O=function(a,b,c){var e,f,g,h,i=a.style;return c=c||U(a),h=c?c[b]:void 0,null==h&&i&&i[b]&&(h=i[b]),V.test(h)&&!xc.test(b)&&(e=i.left,f=a.runtimeStyle,g=f&&f.left,g&&(f.left=a.currentStyle.left),i.left="fontSize"===b?"1em":h,h=i.pixelLeft+"px",i.left=e,g&&(f.left=g)),void 0===h?h:h+""||"auto"});!function(){function a(){var a,b,d=
l.getElementsByTagName("body")[0];d&&(a=l.createElement("div"),b=l.createElement("div"),a.style.cssText=j,d.appendChild(a).appendChild(b),b.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;display:block;padding:1px;border:1px;width:4px;margin-top:1%;top:1%",c.swap(d,null!=d.style.zoom?{zoom:1}:{},function(){e=4===b.offsetWidth}),f=!0,g=!1,h=!0,o.getComputedStyle&&(g="1%"!==(o.getComputedStyle(b,null)||{}).top,f="4px"===(o.getComputedStyle(b,
null)||{width:"4px"}).width),d.removeChild(a),b=d=null)}var b,d,e,f,g,h,i=l.createElement("div"),j="border:0;width:0;height:0;position:absolute;top:0;left:-9999px";i.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";b=i.getElementsByTagName("a")[0];b.style.cssText="float:left;opacity:.5";n.opacity=/^0.5/.test(b.style.opacity);n.cssFloat=!!b.style.cssFloat;i.style.backgroundClip="content-box";i.cloneNode(!0).style.backgroundClip="";n.clearCloneStyle="content-box"===i.style.backgroundClip;
b=i=null;c.extend(n,{reliableHiddenOffsets:function(){if(null!=d)return d;var a,b,c,e=l.createElement("div"),f=l.getElementsByTagName("body")[0];if(f)return e.setAttribute("className","t"),e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",a=l.createElement("div"),a.style.cssText=j,f.appendChild(a).appendChild(e),e.innerHTML="<table><tr><td></td><td>t</td></tr></table>",b=e.getElementsByTagName("td"),b[0].style.cssText="padding:0;margin:0;border:0;display:none",c=0===
b[0].offsetHeight,b[0].style.display="",b[1].style.display="none",d=c&&0===b[0].offsetHeight,f.removeChild(a),d},boxSizing:function(){return null==e&&a(),e},boxSizingReliable:function(){return null==f&&a(),f},pixelPosition:function(){return null==g&&a(),g},reliableMarginRight:function(){var a,b,c,d;if(null==h&&o.getComputedStyle){if(a=l.getElementsByTagName("body")[0],!a)return;b=l.createElement("div");c=l.createElement("div");b.style.cssText=j;a.appendChild(b).appendChild(c);d=c.appendChild(l.createElement("div"));
d.style.cssText=c.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;padding:0;margin:0;border:0";d.style.marginRight=d.style.width="0";c.style.width="1px";h=!parseFloat((o.getComputedStyle(d,null)||{}).marginRight);a.removeChild(b)}return h}})}();c.swap=function(a,b,c,e){var f,g={};for(f in b)g[f]=a.style[f],a.style[f]=b[f];c=c.apply(a,e||[]);for(f in b)a.style[f]=g[f];return c};var Pa=/alpha\([^)]*\)/i,yc=/opacity\s*=\s*([^)]*)/,zc=/^(none|table(?!-c[ea]).+)/,
dc=RegExp("^("+wa+")(.*)$","i"),Ac=RegExp("^([+-])=("+wa+")","i"),Bc={position:"absolute",visibility:"hidden",display:"block"},Nb={letterSpacing:0,fontWeight:400},mb=["Webkit","O","Moz","ms"];c.extend({cssHooks:{opacity:{get:function(a,b){if(b){var c=O(a,"opacity");return""===c?"1":c}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":n.cssFloat?"cssFloat":"styleFloat"},style:function(a,b,d,e){if(a&&
3!==a.nodeType&&8!==a.nodeType&&a.style){var f,g,h,i=c.camelCase(b),j=a.style;if(b=c.cssProps[i]||(c.cssProps[i]=lb(j,i)),h=c.cssHooks[b]||c.cssHooks[i],void 0===d)return h&&"get"in h&&void 0!==(f=h.get(a,!1,e))?f:j[b];if(g=typeof d,"string"===g&&(f=Ac.exec(d))&&(d=(f[1]+1)*f[2]+parseFloat(c.css(a,b)),g="number"),null!=d&&d===d&&("number"!==g||c.cssNumber[i]||(d+="px"),n.clearCloneStyle||""!==d||0!==b.indexOf("background")||(j[b]="inherit"),!(h&&"set"in h&&void 0===(d=h.set(a,d,e)))))try{j[b]="",
j[b]=d}catch(m){}}},css:function(a,b,d,e){var f,g,h,i=c.camelCase(b);return b=c.cssProps[i]||(c.cssProps[i]=lb(a.style,i)),h=c.cssHooks[b]||c.cssHooks[i],h&&"get"in h&&(g=h.get(a,!0,d)),void 0===g&&(g=O(a,b,e)),"normal"===g&&b in Nb&&(g=Nb[b]),""===d||d?(f=parseFloat(g),!0===d||c.isNumeric(f)?f||0:g):g}});c.each(["height","width"],function(a,b){c.cssHooks[b]={get:function(a,e,f){return e?0===a.offsetWidth&&zc.test(c.css(a,"display"))?c.swap(a,Bc,function(){return qb(a,b,f)}):qb(a,b,f):void 0},set:function(a,
e,f){var g=f&&U(a);return ob(a,e,f?pb(a,b,f,n.boxSizing()&&"border-box"===c.css(a,"boxSizing",!1,g),g):0)}}});n.opacity||(c.cssHooks.opacity={get:function(a,b){return yc.test((b&&a.currentStyle?a.currentStyle.filter:a.style.filter)||"")?0.01*parseFloat(RegExp.$1)+"":b?"1":""},set:function(a,b){var d=a.style,e=a.currentStyle,f=c.isNumeric(b)?"alpha(opacity="+100*b+")":"",g=e&&e.filter||d.filter||"";d.zoom=1;(1<=b||""===b)&&""===c.trim(g.replace(Pa,""))&&d.removeAttribute&&(d.removeAttribute("filter"),
""===b||e&&!e.filter)||(d.filter=Pa.test(g)?g.replace(Pa,f):g+" "+f)}});c.cssHooks.marginRight=kb(n.reliableMarginRight,function(a,b){return b?c.swap(a,{display:"inline-block"},O,[a,"marginRight"]):void 0});c.each({margin:"",padding:"",border:"Width"},function(a,b){c.cssHooks[a+b]={expand:function(c){for(var e=0,f={},c="string"==typeof c?c.split(" "):[c];4>e;e++)f[a+T[e]+b]=c[e]||c[e-2]||c[0];return f}};Mb.test(a)||(c.cssHooks[a+b].set=ob)});c.fn.extend({css:function(a,b){return B(this,function(a,
b,f){var g,h={},i=0;if(c.isArray(b)){f=U(a);for(g=b.length;g>i;i++)h[b[i]]=c.css(a,b[i],!1,f);return h}return void 0!==f?c.style(a,b,f):c.css(a,b)},a,b,1<arguments.length)},show:function(){return nb(this,!0)},hide:function(){return nb(this)},toggle:function(a){return"boolean"==typeof a?a?this.show():this.hide():this.each(function(){N(this)?c(this).show():c(this).hide()})}});c.Tween=D;D.prototype={constructor:D,init:function(a,b,d,e,f,g){this.elem=a;this.prop=d;this.easing=f||"swing";this.options=
b;this.start=this.now=this.cur();this.end=e;this.unit=g||(c.cssNumber[d]?"":"px")},cur:function(){var a=D.propHooks[this.prop];return a&&a.get?a.get(this):D.propHooks._default.get(this)},run:function(a){var b,d=D.propHooks[this.prop];return this.pos=b=this.options.duration?c.easing[this.easing](a,this.options.duration*a,0,1,this.options.duration):a,this.now=(this.end-this.start)*b+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),d&&d.set?d.set(this):D.propHooks._default.set(this),
this}};D.prototype.init.prototype=D.prototype;D.propHooks={_default:{get:function(a){var b;return null==a.elem[a.prop]||a.elem.style&&null!=a.elem.style[a.prop]?(b=c.css(a.elem,a.prop,""),b&&"auto"!==b?b:0):a.elem[a.prop]},set:function(a){c.fx.step[a.prop]?c.fx.step[a.prop](a):a.elem.style&&(null!=a.elem.style[c.cssProps[a.prop]]||c.cssHooks[a.prop])?c.style(a.elem,a.prop,a.now+a.unit):a.elem[a.prop]=a.now}}};D.propHooks.scrollTop=D.propHooks.scrollLeft={set:function(a){a.elem.nodeType&&a.elem.parentNode&&
(a.elem[a.prop]=a.now)}};c.easing={linear:function(a){return a},swing:function(a){return 0.5-Math.cos(a*Math.PI)/2}};c.fx=D.prototype.init;c.fx.step={};var F,xa,Cc=/^(?:toggle|show|hide)$/,Ob=RegExp("^(?:([+-])=|)("+wa+")([a-z%]*)$","i"),Dc=/queueHooks$/,oa=[function(a,b,d){var e,f,g,h,i,j,m,l=this,k={},o=a.style,t=a.nodeType&&N(a),s=c._data(a,"fxshow");d.queue||(h=c._queueHooks(a,"fx"),null==h.unqueued&&(h.unqueued=0,i=h.empty.fire,h.empty.fire=function(){h.unqueued||i()}),h.unqueued++,l.always(function(){l.always(function(){h.unqueued--;
c.queue(a,"fx").length||h.empty.fire()})}));1===a.nodeType&&("height"in b||"width"in b)&&(d.overflow=[o.overflow,o.overflowX,o.overflowY],j=c.css(a,"display"),m=ib(a.nodeName),"none"===j&&(j=m),"inline"===j&&"none"===c.css(a,"float")&&(n.inlineBlockNeedsLayout&&"inline"!==m?o.zoom=1:o.display="inline-block"));d.overflow&&(o.overflow="hidden",n.shrinkWrapBlocks()||l.always(function(){o.overflow=d.overflow[0];o.overflowX=d.overflow[1];o.overflowY=d.overflow[2]}));for(e in b)if(f=b[e],Cc.exec(f)){if(delete b[e],
g=g||"toggle"===f,f===(t?"hide":"show")){if("show"!==f||!s||void 0===s[e])continue;t=!0}k[e]=s&&s[e]||c.style(a,e)}if(!c.isEmptyObject(k))for(e in s?"hidden"in s&&(t=s.hidden):s=c._data(a,"fxshow",{}),g&&(s.hidden=!t),t?c(a).show():l.done(function(){c(a).hide()}),l.done(function(){var b;c._removeData(a,"fxshow");for(b in k)c.style(a,b,k[b])}),k)b=sb(t?s[e]:0,e,l),e in s||(s[e]=b.start,t&&(b.end=b.start,b.start="width"===e||"height"===e?1:0))}],ga={"*":[function(a,b){var d=this.createTween(a,b),e=
d.cur(),f=Ob.exec(b),g=f&&f[3]||(c.cssNumber[a]?"":"px"),h=(c.cssNumber[a]||"px"!==g&&+e)&&Ob.exec(c.css(d.elem,a)),i=1,j=20;if(h&&h[3]!==g){g=g||h[3];f=f||[];h=+e||1;do i=i||".5",h/=i,c.style(d.elem,a,h+g);while(i!==(i=d.cur()/e)&&1!==i&&--j)}return f&&(h=d.start=+h||+e||0,d.unit=g,d.end=f[1]?h+(f[1]+1)*f[2]:+f[2]),d}]};c.Animation=c.extend(tb,{tweener:function(a,b){c.isFunction(a)?(b=a,a=["*"]):a=a.split(" ");for(var d,e=0,f=a.length;f>e;e++)d=a[e],ga[d]=ga[d]||[],ga[d].unshift(b)},prefilter:function(a,
b){b?oa.unshift(a):oa.push(a)}});c.speed=function(a,b,d){var e=a&&"object"==typeof a?c.extend({},a):{complete:d||!d&&b||c.isFunction(a)&&a,duration:a,easing:d&&b||b&&!c.isFunction(b)&&b};return e.duration=c.fx.off?0:"number"==typeof e.duration?e.duration:e.duration in c.fx.speeds?c.fx.speeds[e.duration]:c.fx.speeds._default,(null==e.queue||!0===e.queue)&&(e.queue="fx"),e.old=e.complete,e.complete=function(){c.isFunction(e.old)&&e.old.call(this);e.queue&&c.dequeue(this,e.queue)},e};c.fn.extend({fadeTo:function(a,
b,c,e){return this.filter(N).css("opacity",0).show().end().animate({opacity:b},a,c,e)},animate:function(a,b,d,e){var f=c.isEmptyObject(a),g=c.speed(b,d,e),b=function(){var b=tb(this,c.extend({},a),g);(f||c._data(this,"finish"))&&b.stop(!0)};return b.finish=b,f||!1===g.queue?this.each(b):this.queue(g.queue,b)},stop:function(a,b,d){var e=function(a){var b=a.stop;delete a.stop;b(d)};return"string"!=typeof a&&(d=b,b=a,a=void 0),b&&!1!==a&&this.queue(a||"fx",[]),this.each(function(){var b=!0,g=null!=a&&
a+"queueHooks",h=c.timers,i=c._data(this);if(g)i[g]&&i[g].stop&&e(i[g]);else for(g in i)i[g]&&i[g].stop&&Dc.test(g)&&e(i[g]);for(g=h.length;g--;)h[g].elem!==this||null!=a&&h[g].queue!==a||(h[g].anim.stop(d),b=!1,h.splice(g,1));(b||!d)&&c.dequeue(this,a)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var b,d=c._data(this),e=d[a+"queue"];b=d[a+"queueHooks"];var f=c.timers,g=e?e.length:0;d.finish=!0;c.queue(this,a,[]);b&&b.stop&&b.stop.call(this,!0);for(b=f.length;b--;)f[b].elem===
this&&f[b].queue===a&&(f[b].anim.stop(!0),f.splice(b,1));for(b=0;g>b;b++)e[b]&&e[b].finish&&e[b].finish.call(this);delete d.finish})}});c.each(["toggle","show","hide"],function(a,b){var d=c.fn[b];c.fn[b]=function(a,c,g){return null==a||"boolean"==typeof a?d.apply(this,arguments):this.animate(la(b,!0),a,c,g)}});c.each({slideDown:la("show"),slideUp:la("hide"),slideToggle:la("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(a,b){c.fn[a]=function(a,c,f){return this.animate(b,
a,c,f)}});c.timers=[];c.fx.tick=function(){var a,b=c.timers,d=0;for(F=c.now();d<b.length;d++)a=b[d],a()||b[d]!==a||b.splice(d--,1);b.length||c.fx.stop();F=void 0};c.fx.timer=function(a){c.timers.push(a);a()?c.fx.start():c.timers.pop()};c.fx.interval=13;c.fx.start=function(){xa||(xa=setInterval(c.fx.tick,c.fx.interval))};c.fx.stop=function(){clearInterval(xa);xa=null};c.fx.speeds={slow:600,fast:200,_default:400};c.fn.delay=function(a,b){return a=c.fx?c.fx.speeds[a]||a:a,b=b||"fx",this.queue(b,function(b,
c){var f=setTimeout(b,a);c.stop=function(){clearTimeout(f)}})};(function(){var a,b,c,e,f=l.createElement("div");f.setAttribute("className","t");f.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";a=f.getElementsByTagName("a")[0];c=l.createElement("select");e=c.appendChild(l.createElement("option"));b=f.getElementsByTagName("input")[0];a.style.cssText="top:1px";n.getSetAttribute="t"!==f.className;n.style=/top/.test(a.getAttribute("style"));n.hrefNormalized="/a"===a.getAttribute("href");
n.checkOn=!!b.value;n.optSelected=e.selected;n.enctype=!!l.createElement("form").enctype;c.disabled=!0;n.optDisabled=!e.disabled;b=l.createElement("input");b.setAttribute("value","");n.input=""===b.getAttribute("value");b.value="t";b.setAttribute("type","radio");n.radioValue="t"===b.value})();var Ec=/\r/g;c.fn.extend({val:function(a){var b,d,e,f=this[0];if(arguments.length)return e=c.isFunction(a),this.each(function(d){var f;1===this.nodeType&&(f=e?a.call(this,d,c(this).val()):a,null==f?f="":"number"==
typeof f?f+="":c.isArray(f)&&(f=c.map(f,function(a){return null==a?"":a+""})),b=c.valHooks[this.type]||c.valHooks[this.nodeName.toLowerCase()],b&&"set"in b&&void 0!==b.set(this,f,"value")||(this.value=f))});if(f)return b=c.valHooks[f.type]||c.valHooks[f.nodeName.toLowerCase()],b&&"get"in b&&void 0!==(d=b.get(f,"value"))?d:(d=f.value,"string"==typeof d?d.replace(Ec,""):null==d?"":d)}});c.extend({valHooks:{option:{get:function(a){var b=c.find.attr(a,"value");return null!=b?b:c.text(a)}},select:{get:function(a){for(var b,
d=a.options,e=a.selectedIndex,f="select-one"===a.type||0>e,g=f?null:[],h=f?e+1:d.length,i=0>e?h:f?e:0;h>i;i++)if(b=d[i],!(!b.selected&&i!==e||(n.optDisabled?b.disabled:null!==b.getAttribute("disabled"))||b.parentNode.disabled&&c.nodeName(b.parentNode,"optgroup"))){if(a=c(b).val(),f)return a;g.push(a)}return g},set:function(a,b){for(var d,e,f=a.options,g=c.makeArray(b),h=f.length;h--;)if(e=f[h],0<=c.inArray(c.valHooks.option.get(e),g))try{e.selected=d=!0}catch(i){e.scrollHeight}else e.selected=!1;
return d||(a.selectedIndex=-1),f}}}});c.each(["radio","checkbox"],function(){c.valHooks[this]={set:function(a,b){return c.isArray(b)?a.checked=0<=c.inArray(c(a).val(),b):void 0}};n.checkOn||(c.valHooks[this].get=function(a){return null===a.getAttribute("value")?"on":a.value})});var da,Pb,Q=c.expr.attrHandle,Qa=/^(?:checked|selected)$/i,R=n.getSetAttribute,ya=n.input;c.fn.extend({attr:function(a,b){return B(this,c.attr,a,b,1<arguments.length)},removeAttr:function(a){return this.each(function(){c.removeAttr(this,
a)})}});c.extend({attr:function(a,b,d){var e,f,g=a.nodeType;if(a&&3!==g&&8!==g&&2!==g)return typeof a.getAttribute===z?c.prop(a,b,d):(1===g&&c.isXMLDoc(a)||(b=b.toLowerCase(),e=c.attrHooks[b]||(c.expr.match.bool.test(b)?Pb:da)),void 0===d?e&&"get"in e&&null!==(f=e.get(a,b))?f:(f=c.find.attr(a,b),null==f?void 0:f):null!==d?e&&"set"in e&&void 0!==(f=e.set(a,d,b))?f:(a.setAttribute(b,d+""),d):void c.removeAttr(a,b))},removeAttr:function(a,b){var d,e,f=0,g=b&&b.match(H);if(g&&1===a.nodeType)for(;d=g[f++];)e=
c.propFix[d]||d,c.expr.match.bool.test(d)?ya&&R||!Qa.test(d)?a[e]=!1:a[c.camelCase("default-"+d)]=a[e]=!1:c.attr(a,d,""),a.removeAttribute(R?d:e)},attrHooks:{type:{set:function(a,b){if(!n.radioValue&&"radio"===b&&c.nodeName(a,"input")){var d=a.value;return a.setAttribute("type",b),d&&(a.value=d),b}}}}});Pb={set:function(a,b,d){return!1===b?c.removeAttr(a,d):ya&&R||!Qa.test(d)?a.setAttribute(!R&&c.propFix[d]||d,d):a[c.camelCase("default-"+d)]=a[d]=!0,d}};c.each(c.expr.match.bool.source.match(/\w+/g),
function(a,b){var d=Q[b]||c.find.attr;Q[b]=ya&&R||!Qa.test(b)?function(a,b,c){var h,i;return c||(i=Q[b],Q[b]=h,h=null!=d(a,b,c)?b.toLowerCase():null,Q[b]=i),h}:function(a,b,d){return d?void 0:a[c.camelCase("default-"+b)]?b.toLowerCase():null}});ya&&R||(c.attrHooks.value={set:function(a,b,d){return c.nodeName(a,"input")?void(a.defaultValue=b):da&&da.set(a,b,d)}});R||(da={set:function(a,b,c){var e=a.getAttributeNode(c);return e||a.setAttributeNode(e=a.ownerDocument.createAttribute(c)),e.value=b+="",
"value"===c||b===a.getAttribute(c)?b:void 0}},Q.id=Q.name=Q.coords=function(a,b,c){var e;return c?void 0:(e=a.getAttributeNode(b))&&""!==e.value?e.value:null},c.valHooks.button={get:function(a,b){var c=a.getAttributeNode(b);return c&&c.specified?c.value:void 0},set:da.set},c.attrHooks.contenteditable={set:function(a,b,c){da.set(a,""===b?!1:b,c)}},c.each(["width","height"],function(a,b){c.attrHooks[b]={set:function(a,c){return""===c?(a.setAttribute(b,"auto"),c):void 0}}}));n.style||(c.attrHooks.style=
{get:function(a){return a.style.cssText||void 0},set:function(a,b){return a.style.cssText=b+""}});var Fc=/^(?:input|select|textarea|button|object)$/i,Gc=/^(?:a|area)$/i;c.fn.extend({prop:function(a,b){return B(this,c.prop,a,b,1<arguments.length)},removeProp:function(a){return a=c.propFix[a]||a,this.each(function(){try{this[a]=void 0,delete this[a]}catch(b){}})}});c.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(a,b,d){var e,f,g,h=a.nodeType;if(a&&3!==h&&8!==h&&2!==h)return g=
1!==h||!c.isXMLDoc(a),g&&(b=c.propFix[b]||b,f=c.propHooks[b]),void 0!==d?f&&"set"in f&&void 0!==(e=f.set(a,d,b))?e:a[b]=d:f&&"get"in f&&null!==(e=f.get(a,b))?e:a[b]},propHooks:{tabIndex:{get:function(a){var b=c.find.attr(a,"tabindex");return b?parseInt(b,10):Fc.test(a.nodeName)||Gc.test(a.nodeName)&&a.href?0:-1}}}});n.hrefNormalized||c.each(["href","src"],function(a,b){c.propHooks[b]={get:function(a){return a.getAttribute(b,4)}}});n.optSelected||(c.propHooks.selected={get:function(a){a=a.parentNode;
return a&&(a.selectedIndex,a.parentNode&&a.parentNode.selectedIndex),null}});c.each("tabIndex readOnly maxLength cellSpacing cellPadding rowSpan colSpan useMap frameBorder contentEditable".split(" "),function(){c.propFix[this.toLowerCase()]=this});n.enctype||(c.propFix.enctype="encoding");var Ra=/[\t\r\n\f]/g;c.fn.extend({addClass:function(a){var b,d,e,f,g,h=0,i=this.length;b="string"==typeof a&&a;if(c.isFunction(a))return this.each(function(b){c(this).addClass(a.call(this,b,this.className))});if(b)for(b=
(a||"").match(H)||[];i>h;h++)if(d=this[h],e=1===d.nodeType&&(d.className?(" "+d.className+" ").replace(Ra," "):" ")){for(g=0;f=b[g++];)0>e.indexOf(" "+f+" ")&&(e+=f+" ");e=c.trim(e);d.className!==e&&(d.className=e)}return this},removeClass:function(a){var b,d,e,f,g,h=0,i=this.length;b=0===arguments.length||"string"==typeof a&&a;if(c.isFunction(a))return this.each(function(b){c(this).removeClass(a.call(this,b,this.className))});if(b)for(b=(a||"").match(H)||[];i>h;h++)if(d=this[h],e=1===d.nodeType&&
(d.className?(" "+d.className+" ").replace(Ra," "):"")){for(g=0;f=b[g++];)for(;0<=e.indexOf(" "+f+" ");)e=e.replace(" "+f+" "," ");e=a?c.trim(e):"";d.className!==e&&(d.className=e)}return this},toggleClass:function(a,b){var d=typeof a;return"boolean"==typeof b&&"string"===d?b?this.addClass(a):this.removeClass(a):this.each(c.isFunction(a)?function(d){c(this).toggleClass(a.call(this,d,this.className,b),b)}:function(){if("string"===d)for(var b,f=0,g=c(this),h=a.match(H)||[];b=h[f++];)g.hasClass(b)?g.removeClass(b):
g.addClass(b);else(d===z||"boolean"===d)&&(this.className&&c._data(this,"__className__",this.className),this.className=this.className||!1===a?"":c._data(this,"__className__")||"")})},hasClass:function(a){for(var a=" "+a+" ",b=0,c=this.length;c>b;b++)if(1===this[b].nodeType&&0<=(" "+this[b].className+" ").replace(Ra," ").indexOf(a))return!0;return!1}});c.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),
function(a,b){c.fn[b]=function(a,c){return arguments.length>0?this.on(b,null,a,c):this.trigger(b)}});c.fn.extend({hover:function(a,b){return this.mouseenter(a).mouseleave(b||a)},bind:function(a,b,c){return this.on(a,null,b,c)},unbind:function(a,b){return this.off(a,null,b)},delegate:function(a,b,c,e){return this.on(b,a,c,e)},undelegate:function(a,b,c){return 1===arguments.length?this.off(a,"**"):this.off(b,a||"**",c)}});var Sa=c.now(),Ta=/\?/,Hc=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;
c.parseJSON=function(a){if(o.JSON&&o.JSON.parse)return o.JSON.parse(a+"");var b,d=null,e=c.trim(a+"");return e&&!c.trim(e.replace(Hc,function(a,c,e,i){return b&&c&&(d=0),0===d?a:(b=e||c,d+=!i-!e,"")}))?Function("return "+e)():c.error("Invalid JSON: "+a)};c.parseXML=function(a){var b,d;if(!a||"string"!=typeof a)return null;try{o.DOMParser?(d=new DOMParser,b=d.parseFromString(a,"text/xml")):(b=new ActiveXObject("Microsoft.XMLDOM"),b.async="false",b.loadXML(a))}catch(e){b=void 0}return b&&b.documentElement&&
!b.getElementsByTagName("parsererror").length||c.error("Invalid XML: "+a),b};var S,x,Ic=/#.*$/,Qb=/([?&])_=[^&]*/,Jc=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Kc=/^(?:GET|HEAD)$/,Lc=/^\/\//,Rb=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Sb={},Ga={},Tb="*/".concat("*");try{x=location.href}catch(Sc){x=l.createElement("a"),x.href="",x=x.href}S=Rb.exec(x.toLowerCase())||[];c.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:x,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(S[1]),
global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Tb,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":c.parseJSON,"text xml":c.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(a,b){return b?Ha(Ha(a,
c.ajaxSettings),b):Ha(c.ajaxSettings,a)},ajaxPrefilter:ub(Sb),ajaxTransport:ub(Ga),ajax:function(a,b){function d(a,b,d,e){var f,l,q,r,G=b;if(2!==z){z=2;i&&clearTimeout(i);m=void 0;h=e||"";p.readyState=0<a?4:0;e=200<=a&&300>a||304===a;if(d){q=k;for(var A=p,y,B,x,w,C=q.contents,E=q.dataTypes;"*"===E[0];)E.shift(),void 0===B&&(B=q.mimeType||A.getResponseHeader("Content-Type"));if(B)for(w in C)if(C[w]&&C[w].test(B)){E.unshift(w);break}if(E[0]in d)x=E[0];else{for(w in d){if(!E[0]||q.converters[w+" "+E[0]]){x=
w;break}y||(y=w)}x=x||y}q=x?(x!==E[0]&&E.unshift(x),d[x]):void 0}var I;a:{d=k;y=q;B=p;x=e;var v,H,F;q={};A=d.dataTypes.slice();if(A[1])for(v in d.converters)q[v.toLowerCase()]=d.converters[v];for(w=A.shift();w;)if(d.responseFields[w]&&(B[d.responseFields[w]]=y),!F&&x&&d.dataFilter&&(y=d.dataFilter(y,d.dataType)),F=w,w=A.shift())if("*"===w)w=F;else if("*"!==F&&F!==w){if(v=q[F+" "+w]||q["* "+w],!v)for(I in q)if(H=I.split(" "),H[1]===w&&(v=q[F+" "+H[0]]||q["* "+H[0]])){!0===v?v=q[I]:!0!==q[I]&&(w=H[0],
A.unshift(H[1]));break}if(!0!==v)if(v&&d["throws"])y=v(y);else try{y=v(y)}catch(L){I={state:"parsererror",error:v?L:"No conversion from "+F+" to "+w};break a}}I={state:"success",data:y}}q=I;e?(k.ifModified&&(r=p.getResponseHeader("Last-Modified"),r&&(c.lastModified[g]=r),r=p.getResponseHeader("etag"),r&&(c.etag[g]=r)),204===a||"HEAD"===k.type?G="nocontent":304===a?G="notmodified":(G=q.state,f=q.data,l=q.error,e=!l)):(l=G,(a||!G)&&(G="error",0>a&&(a=0)));p.status=a;p.statusText=(b||G)+"";e?s.resolveWith(n,
[f,G,p]):s.rejectWith(n,[p,G,l]);p.statusCode(D);D=void 0;j&&o.trigger(e?"ajaxSuccess":"ajaxError",[p,k,e?f:l]);t.fireWith(n,[p,G]);j&&(o.trigger("ajaxComplete",[p,k]),--c.active||c.event.trigger("ajaxStop"))}}"object"==typeof a&&(b=a,a=void 0);var b=b||{},e,f,g,h,i,j,m,l,k=c.ajaxSetup({},b),n=k.context||k,o=k.context&&(n.nodeType||n.jquery)?c(n):c.event,s=c.Deferred(),t=c.Callbacks("once memory"),D=k.statusCode||{},A={},B={},z=0,v="canceled",p={readyState:0,getResponseHeader:function(a){var b;if(2===
z){if(!l)for(l={};b=Jc.exec(h);)l[b[1].toLowerCase()]=b[2];b=l[a.toLowerCase()]}return null==b?null:b},getAllResponseHeaders:function(){return 2===z?h:null},setRequestHeader:function(a,b){var c=a.toLowerCase();return z||(a=B[c]=B[c]||a,A[a]=b),this},overrideMimeType:function(a){return z||(k.mimeType=a),this},statusCode:function(a){var b;if(a)if(2>z)for(b in a)D[b]=[D[b],a[b]];else p.always(a[p.status]);return this},abort:function(a){a=a||v;return m&&m.abort(a),d(0,a),this}};if(s.promise(p).complete=
t.add,p.success=p.done,p.error=p.fail,k.url=((a||k.url||x)+"").replace(Ic,"").replace(Lc,S[1]+"//"),k.type=b.method||b.type||k.method||k.type,k.dataTypes=c.trim(k.dataType||"*").toLowerCase().match(H)||[""],null==k.crossDomain&&(e=Rb.exec(k.url.toLowerCase()),k.crossDomain=!(!e||e[1]===S[1]&&e[2]===S[2]&&(e[3]||("http:"===e[1]?"80":"443"))===(S[3]||("http:"===S[1]?"80":"443")))),k.data&&k.processData&&"string"!=typeof k.data&&(k.data=c.param(k.data,k.traditional)),vb(Sb,k,b,p),2===z)return p;(j=k.global)&&
0===c.active++&&c.event.trigger("ajaxStart");k.type=k.type.toUpperCase();k.hasContent=!Kc.test(k.type);g=k.url;k.hasContent||(k.data&&(g=k.url+=(Ta.test(g)?"&":"?")+k.data,delete k.data),!1===k.cache&&(k.url=Qb.test(g)?g.replace(Qb,"$1_="+Sa++):g+(Ta.test(g)?"&":"?")+"_="+Sa++));k.ifModified&&(c.lastModified[g]&&p.setRequestHeader("If-Modified-Since",c.lastModified[g]),c.etag[g]&&p.setRequestHeader("If-None-Match",c.etag[g]));(k.data&&k.hasContent&&!1!==k.contentType||b.contentType)&&p.setRequestHeader("Content-Type",
k.contentType);p.setRequestHeader("Accept",k.dataTypes[0]&&k.accepts[k.dataTypes[0]]?k.accepts[k.dataTypes[0]]+("*"!==k.dataTypes[0]?", "+Tb+"; q=0.01":""):k.accepts["*"]);for(f in k.headers)p.setRequestHeader(f,k.headers[f]);if(k.beforeSend&&(!1===k.beforeSend.call(n,p,k)||2===z))return p.abort();v="abort";for(f in{success:1,error:1,complete:1})p[f](k[f]);if(m=vb(Ga,k,b,p)){p.readyState=1;j&&o.trigger("ajaxSend",[p,k]);k.async&&0<k.timeout&&(i=setTimeout(function(){p.abort("timeout")},k.timeout));
try{z=1,m.send(A,d)}catch(r){if(!(2>z))throw r;d(-1,r)}}else d(-1,"No Transport");return p},getJSON:function(a,b,d){return c.get(a,b,d,"json")},getScript:function(a,b){return c.get(a,void 0,b,"script")}});c.each(["get","post"],function(a,b){c[b]=function(a,e,f,g){return c.isFunction(e)&&(g=g||f,f=e,e=void 0),c.ajax({url:a,type:b,dataType:g,data:e,success:f})}});c.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(a,b){c.fn[b]=function(a){return this.on(b,a)}});
c._evalUrl=function(a){return c.ajax({url:a,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})};c.fn.extend({wrapAll:function(a){if(c.isFunction(a))return this.each(function(b){c(this).wrapAll(a.call(this,b))});if(this[0]){var b=c(a,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&b.insertBefore(this[0]);b.map(function(){for(var a=this;a.firstChild&&1===a.firstChild.nodeType;)a=a.firstChild;return a}).append(this)}return this},wrapInner:function(a){return this.each(c.isFunction(a)?
function(b){c(this).wrapInner(a.call(this,b))}:function(){var b=c(this),d=b.contents();d.length?d.wrapAll(a):b.append(a)})},wrap:function(a){var b=c.isFunction(a);return this.each(function(d){c(this).wrapAll(b?a.call(this,d):a)})},unwrap:function(){return this.parent().each(function(){c.nodeName(this,"body")||c(this).replaceWith(this.childNodes)}).end()}});c.expr.filters.hidden=function(a){return 0>=a.offsetWidth&&0>=a.offsetHeight||!n.reliableHiddenOffsets()&&"none"===(a.style&&a.style.display||
c.css(a,"display"))};c.expr.filters.visible=function(a){return!c.expr.filters.hidden(a)};var Mc=/%20/g,fc=/\[\]$/,Ub=/\r?\n/g,Nc=/^(?:submit|button|image|reset|file)$/i,Oc=/^(?:input|select|textarea|keygen)/i;c.param=function(a,b){var d,e=[],f=function(a,b){b=c.isFunction(b)?b():null==b?"":b;e[e.length]=encodeURIComponent(a)+"="+encodeURIComponent(b)};if(void 0===b&&(b=c.ajaxSettings&&c.ajaxSettings.traditional),c.isArray(a)||a.jquery&&!c.isPlainObject(a))c.each(a,function(){f(this.name,this.value)});
else for(d in a)Ia(d,a[d],b,f);return e.join("&").replace(Mc,"+")};c.fn.extend({serialize:function(){return c.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var a=c.prop(this,"elements");return a?c.makeArray(a):this}).filter(function(){var a=this.type;return this.name&&!c(this).is(":disabled")&&Oc.test(this.nodeName)&&!Nc.test(a)&&(this.checked||!Ea.test(a))}).map(function(a,b){var d=c(this).val();return null==d?null:c.isArray(d)?c.map(d,function(a){return{name:b.name,
value:a.replace(Ub,"\r\n")}}):{name:b.name,value:d.replace(Ub,"\r\n")}}).get()}});c.ajaxSettings.xhr=void 0!==o.ActiveXObject?function(){var a;if(!(a=!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&wb()))a:{try{a=new o.ActiveXObject("Microsoft.XMLHTTP");break a}catch(b){}a=void 0}return a}:wb;var Pc=0,za={},Aa=c.ajaxSettings.xhr();o.ActiveXObject&&c(o).on("unload",function(){for(var a in za)za[a](void 0,!0)});n.cors=!!Aa&&"withCredentials"in Aa;(Aa=n.ajax=!!Aa)&&c.ajaxTransport(function(a){if(!a.crossDomain||
n.cors){var b;return{send:function(d,e){var f,g=a.xhr(),h=++Pc;if(g.open(a.type,a.url,a.async,a.username,a.password),a.xhrFields)for(f in a.xhrFields)g[f]=a.xhrFields[f];a.mimeType&&g.overrideMimeType&&g.overrideMimeType(a.mimeType);a.crossDomain||d["X-Requested-With"]||(d["X-Requested-With"]="XMLHttpRequest");for(f in d)void 0!==d[f]&&g.setRequestHeader(f,d[f]+"");g.send(a.hasContent&&a.data||null);b=function(d,f){var m,l,k;if(b&&(f||4===g.readyState))if(delete za[h],b=void 0,g.onreadystatechange=
c.noop,f)4!==g.readyState&&g.abort();else{k={};m=g.status;"string"==typeof g.responseText&&(k.text=g.responseText);try{l=g.statusText}catch(n){l=""}m||!a.isLocal||a.crossDomain?1223===m&&(m=204):m=k.text?200:404}k&&e(m,l,k,g.getAllResponseHeaders())};a.async?4===g.readyState?setTimeout(b):g.onreadystatechange=za[h]=b:b()},abort:function(){b&&b(void 0,!0)}}}});c.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},
converters:{"text script":function(a){return c.globalEval(a),a}}});c.ajaxPrefilter("script",function(a){void 0===a.cache&&(a.cache=!1);a.crossDomain&&(a.type="GET",a.global=!1)});c.ajaxTransport("script",function(a){if(a.crossDomain){var b,d=l.head||c("head")[0]||l.documentElement;return{send:function(c,f){b=l.createElement("script");b.async=!0;a.scriptCharset&&(b.charset=a.scriptCharset);b.src=a.url;b.onload=b.onreadystatechange=function(a,c){(c||!b.readyState||/loaded|complete/.test(b.readyState))&&
(b.onload=b.onreadystatechange=null,b.parentNode&&b.parentNode.removeChild(b),b=null,c||f(200,"success"))};d.insertBefore(b,d.firstChild)},abort:function(){b&&b.onload(void 0,!0)}}}});var Vb=[],Ua=/(=)\?(?=&|$)|\?\?/;c.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var a=Vb.pop()||c.expando+"_"+Sa++;return this[a]=!0,a}});c.ajaxPrefilter("json jsonp",function(a,b,d){var e,f,g,h=!1!==a.jsonp&&(Ua.test(a.url)?"url":"string"==typeof a.data&&!(a.contentType||"").indexOf("application/x-www-form-urlencoded")&&
Ua.test(a.data)&&"data");return h||"jsonp"===a.dataTypes[0]?(e=a.jsonpCallback=c.isFunction(a.jsonpCallback)?a.jsonpCallback():a.jsonpCallback,h?a[h]=a[h].replace(Ua,"$1"+e):!1!==a.jsonp&&(a.url+=(Ta.test(a.url)?"&":"?")+a.jsonp+"="+e),a.converters["script json"]=function(){return g||c.error(e+" was not called"),g[0]},a.dataTypes[0]="json",f=o[e],o[e]=function(){g=arguments},d.always(function(){o[e]=f;a[e]&&(a.jsonpCallback=b.jsonpCallback,Vb.push(e));g&&c.isFunction(f)&&f(g[0]);g=f=void 0}),"script"):
void 0});c.parseHTML=function(a,b,d){if(!a||"string"!=typeof a)return null;"boolean"==typeof b&&(d=b,b=!1);var b=b||l,e=Db.exec(a),d=!d&&[];return e?[b.createElement(e[1])]:(e=c.buildFragment([a],b,d),d&&d.length&&c(d).remove(),c.merge([],e.childNodes))};var Wb=c.fn.load;c.fn.load=function(a,b,d){if("string"!=typeof a&&Wb)return Wb.apply(this,arguments);var e,f,g,h=this,i=a.indexOf(" ");return 0<=i&&(e=a.slice(i,a.length),a=a.slice(0,i)),c.isFunction(b)?(d=b,b=void 0):b&&"object"==typeof b&&(g="POST"),
0<h.length&&c.ajax({url:a,type:g,dataType:"html",data:b}).done(function(a){f=arguments;h.html(e?c("<div>").append(c.parseHTML(a)).find(e):a)}).complete(d&&function(a,b){h.each(d,f||[a.responseText,b,a])}),this};c.expr.filters.animated=function(a){return c.grep(c.timers,function(b){return a===b.elem}).length};var Xb=o.document.documentElement;c.offset={setOffset:function(a,b,d){var e,f,g,h,i,j,l=c.css(a,"position"),n=c(a),k={};"static"===l&&(a.style.position="relative");i=n.offset();g=c.css(a,"top");
j=c.css(a,"left");("absolute"===l||"fixed"===l)&&-1<c.inArray("auto",[g,j])?(e=n.position(),h=e.top,f=e.left):(h=parseFloat(g)||0,f=parseFloat(j)||0);c.isFunction(b)&&(b=b.call(a,d,i));null!=b.top&&(k.top=b.top-i.top+h);null!=b.left&&(k.left=b.left-i.left+f);"using"in b?b.using.call(a,k):n.css(k)}};c.fn.extend({offset:function(a){if(arguments.length)return void 0===a?this:this.each(function(b){c.offset.setOffset(this,a,b)});var b,d,e={top:0,left:0},f=this[0],g=f&&f.ownerDocument;if(g)return b=g.documentElement,
c.contains(b,f)?(typeof f.getBoundingClientRect!==z&&(e=f.getBoundingClientRect()),d=xb(g),{top:e.top+(d.pageYOffset||b.scrollTop)-(b.clientTop||0),left:e.left+(d.pageXOffset||b.scrollLeft)-(b.clientLeft||0)}):e},position:function(){if(this[0]){var a,b,d={top:0,left:0},e=this[0];return"fixed"===c.css(e,"position")?b=e.getBoundingClientRect():(a=this.offsetParent(),b=this.offset(),c.nodeName(a[0],"html")||(d=a.offset()),d.top+=c.css(a[0],"borderTopWidth",!0),d.left+=c.css(a[0],"borderLeftWidth",!0)),
{top:b.top-d.top-c.css(e,"marginTop",!0),left:b.left-d.left-c.css(e,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var a=this.offsetParent||Xb;a&&!c.nodeName(a,"html")&&"static"===c.css(a,"position");)a=a.offsetParent;return a||Xb})}});c.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(a,b){var d=/Y/.test(b);c.fn[a]=function(e){return B(this,function(a,e,h){var i=xb(a);return void 0===h?i?b in i?i[b]:i.document.documentElement[e]:a[e]:void(i?i.scrollTo(d?
c(i).scrollLeft():h,d?h:c(i).scrollTop()):a[e]=h)},a,e,arguments.length,null)}});c.each(["top","left"],function(a,b){c.cssHooks[b]=kb(n.pixelPosition,function(a,e){return e?(e=O(a,b),V.test(e)?c(a).position()[b]+"px":e):void 0})});c.each({Height:"height",Width:"width"},function(a,b){c.each({padding:"inner"+a,content:b,"":"outer"+a},function(d,e){c.fn[e]=function(e,g){var h=arguments.length&&(d||"boolean"!=typeof e),i=d||(!0===e||!0===g?"margin":"border");return B(this,function(b,d,e){var f;return c.isWindow(b)?
b.document.documentElement["client"+a]:9===b.nodeType?(f=b.documentElement,Math.max(b.body["scroll"+a],f["scroll"+a],b.body["offset"+a],f["offset"+a],f["client"+a])):void 0===e?c.css(b,d,i):c.style(b,d,e,i)},b,h?e:void 0,h,null)}})});c.fn.size=function(){return this.length};c.fn.andSelf=c.fn.addBack;"function"==typeof define&&define.amd&&define("jquery",[],function(){return c});var Qc=o.jQuery,Rc=o.$;return c.noConflict=function(a){return o.$===c&&(o.$=Rc),a&&o.jQuery===c&&(o.jQuery=Qc),c},typeof ea===
z&&(o.jQuery=o.$=c),c});