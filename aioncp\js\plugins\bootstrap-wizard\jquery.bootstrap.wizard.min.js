/*!
 * jQuery twitter bootstrap wizard plugin
 * Examples and documentation at: http://github.com/VinceG/twitter-bootstrap-wizard
 * version 1.0
 * Requires jQuery v1.3.2 or later
 * Supports Bootstrap 2.2.x, 2.3.x, 3.0
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 * Authors: <AUTHORS>
 */
!function(n){var t=function(t,i){var t=n(t),e=this,o='li:has([data-toggle="tab"])',a=[],r=n.extend({},n.fn.bootstrapWizard.defaults,i),s=null,l=null;this.rebindClick=function(n,t){n.unbind("click",t).bind("click",t)},this.fixNavigationButtons=function(){return s.length||(l.find("a:first").tab("show"),s=l.find(o+":first")),n(r.previousSelector,t).toggleClass("disabled",e.firstIndex()>=e.currentIndex()),n(r.nextSelector,t).toggleClass("disabled",e.currentIndex()>=e.navigationLength()),n(r.nextSelector,t).toggleClass("hidden",e.currentIndex()>=e.navigationLength()&&n(r.finishSelector,t).length>0),n(r.lastSelector,t).toggleClass("hidden",e.currentIndex()>=e.navigationLength()&&n(r.finishSelector,t).length>0),n(r.finishSelector,t).toggleClass("hidden",e.currentIndex()<e.navigationLength()),n(r.backSelector,t).toggleClass("disabled",0==a.length),n(r.backSelector,t).toggleClass("hidden",e.currentIndex()>=e.navigationLength()&&n(r.finishSelector,t).length>0),e.rebindClick(n(r.nextSelector,t),e.next),e.rebindClick(n(r.previousSelector,t),e.previous),e.rebindClick(n(r.lastSelector,t),e.last),e.rebindClick(n(r.firstSelector,t),e.first),e.rebindClick(n(r.finishSelector,t),e.finish),e.rebindClick(n(r.backSelector,t),e.back),r.onTabShow&&"function"==typeof r.onTabShow&&r.onTabShow(s,l,e.currentIndex())===!1?!1:void 0},this.next=function(n){if(t.hasClass("last"))return!1;if(r.onNext&&"function"==typeof r.onNext&&r.onNext(s,l,e.nextIndex())===!1)return!1;var i=e.currentIndex(),d=e.nextIndex();d>e.navigationLength()||(a.push(i),l.find(o+(r.withVisible?":visible":"")+":eq("+d+") a").tab("show"))},this.previous=function(n){if(t.hasClass("first"))return!1;if(r.onPrevious&&"function"==typeof r.onPrevious&&r.onPrevious(s,l,e.previousIndex())===!1)return!1;var i=e.currentIndex(),d=e.previousIndex();0>d||(a.push(i),l.find(o+(r.withVisible?":visible":"")+":eq("+d+") a").tab("show"))},this.first=function(n){return r.onFirst&&"function"==typeof r.onFirst&&r.onFirst(s,l,e.firstIndex())===!1?!1:t.hasClass("disabled")?!1:(a.push(e.currentIndex()),void l.find(o+":eq(0) a").tab("show"))},this.last=function(n){return r.onLast&&"function"==typeof r.onLast&&r.onLast(s,l,e.lastIndex())===!1?!1:t.hasClass("disabled")?!1:(a.push(e.currentIndex()),void l.find(o+":eq("+e.navigationLength()+") a").tab("show"))},this.finish=function(n){r.onFinish&&"function"==typeof r.onFinish&&r.onFinish(s,l,e.lastIndex())},this.back=function(){if(0==a.length)return null;var n=a.pop();return r.onBack&&"function"==typeof r.onBack&&r.onBack(s,l,n)===!1?(a.push(n),!1):void t.find(o+":eq("+n+") a").tab("show")},this.currentIndex=function(){return l.find(o).index(s)},this.firstIndex=function(){return 0},this.lastIndex=function(){return e.navigationLength()},this.getIndex=function(n){return l.find(o).index(n)},this.nextIndex=function(){return l.find(o).index(s)+1},this.previousIndex=function(){return l.find(o).index(s)-1},this.navigationLength=function(){return l.find(o).length-1},this.activeTab=function(){return s},this.nextTab=function(){return l.find(o+":eq("+(e.currentIndex()+1)+")").length?l.find(o+":eq("+(e.currentIndex()+1)+")"):null},this.previousTab=function(){return e.currentIndex()<=0?null:l.find(o+":eq("+parseInt(e.currentIndex()-1)+")")},this.show=function(n){var i=isNaN(n)?t.find(o+" a[href=#"+n+"]"):t.find(o+":eq("+n+") a");i.length>0&&(a.push(e.currentIndex()),i.tab("show"))},this.disable=function(n){l.find(o+":eq("+n+")").addClass("disabled")},this.enable=function(n){l.find(o+":eq("+n+")").removeClass("disabled")},this.hide=function(n){l.find(o+":eq("+n+")").hide()},this.display=function(n){l.find(o+":eq("+n+")").show()},this.remove=function(t){var i=t[0],e="undefined"!=typeof t[1]?t[1]:!1,a=l.find(o+":eq("+i+")");if(e){var r=a.find("a").attr("href");n(r).remove()}a.remove()};var d=function(t){var i=l.find(o),a=i.index(n(t.currentTarget).parent(o)),d=n(i[a]);return r.onTabClick&&"function"==typeof r.onTabClick&&r.onTabClick(s,l,e.currentIndex(),a,d)===!1?!1:void 0},u=function(t){var i=n(t.target).parent(),a=l.find(o).index(i);return i.hasClass("disabled")?!1:r.onTabChange&&"function"==typeof r.onTabChange&&r.onTabChange(s,l,e.currentIndex(),a)===!1?!1:(s=i,void e.fixNavigationButtons())};this.resetWizard=function(){n('a[data-toggle="tab"]',l).off("click",d),n('a[data-toggle="tab"]',l).off("shown shown.bs.tab",u),l=t.find("ul:first",t),s=l.find(o+".active",t),n('a[data-toggle="tab"]',l).on("click",d),n('a[data-toggle="tab"]',l).on("shown shown.bs.tab",u),e.fixNavigationButtons()},l=t.find("ul:first",t),s=l.find(o+".active",t),l.hasClass(r.tabClass)||l.addClass(r.tabClass),r.onInit&&"function"==typeof r.onInit&&r.onInit(s,l,0),r.onShow&&"function"==typeof r.onShow&&r.onShow(s,l,e.nextIndex()),n('a[data-toggle="tab"]',l).on("click",d),n('a[data-toggle="tab"]',l).on("shown shown.bs.tab",u)};n.fn.bootstrapWizard=function(i){if("string"==typeof i){var e=Array.prototype.slice.call(arguments,1);return 1===e.length&&e.toString(),this.data("bootstrapWizard")[i](e)}return this.each(function(e){var o=n(this);if(!o.data("bootstrapWizard")){var a=new t(o,i);o.data("bootstrapWizard",a),a.fixNavigationButtons()}})},n.fn.bootstrapWizard.defaults={withVisible:!0,tabClass:"nav nav-pills",nextSelector:".wizard li.next",previousSelector:".wizard li.previous",firstSelector:".wizard li.first",lastSelector:".wizard li.last",finishSelector:".wizard li.finish",backSelector:".wizard li.back",onShow:null,onInit:null,onNext:null,onPrevious:null,onLast:null,onFirst:null,onFinish:null,onBack:null,onTabChange:null,onTabClick:null,onTabShow:null}}(jQuery);