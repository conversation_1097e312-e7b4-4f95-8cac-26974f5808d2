/* Golden Theme CSS - <PERSON><PERSON> Blitz Style */
/* Root Variables for Golden Theme */
:root {
    --golden-primary: #FFD700;
    --golden-secondary: #FFA500;
    --golden-dark: #DAA520;
    --golden-light: #FFEF94;
    --golden-accent: #B8860B;
    --dark-bg-primary: #1a1a1a;
    --dark-bg-secondary: #2d2d2d;
    --dark-bg-tertiary: #3a3a3a;
    --dark-text-primary: #ffffff;
    --dark-text-secondary: #cccccc;
    --dark-text-muted: #999999;
    --golden-gradient: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #DAA520 100%);
    --golden-gradient-hover: linear-gradient(135deg, #FFEF94 0%, #FFD700 50%, #FFA500 100%);
    --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #3a3a3a 100%);
    --golden-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    --golden-glow: 0 0 20px rgba(255, 215, 0, 0.5);
    --dark-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

/* Body and Base Styling */
body {
    background: var(--dark-bg-primary) !important;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(218, 165, 32, 0.05) 0%, transparent 50%) !important;
    color: var(--dark-text-primary) !important;
    font-family: 'Open Sans', sans-serif !important;
    min-height: 100vh !important;
}

/* Override any white backgrounds */
* {
    background-color: transparent !important;
}

/* Specific white background overrides */
.white-bg, .bg-white, .background-white {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
}

/* Content area backgrounds */
.content, .main-content, .page-content, .content-wrapper {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
}

/* Any remaining white backgrounds */
div[style*="background-color: white"],
div[style*="background-color: #ffffff"],
div[style*="background-color: #fff"],
div[style*="background: white"],
div[style*="background: #ffffff"],
div[style*="background: #fff"] {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
}

/* Container and Layout */
.container {
    background: transparent !important;
}

.container-fluid {
    background: transparent !important;
}

/* Page Layout Enhancements */
.page-wrapper {
    background: transparent !important;
    position: relative !important;
}

.page-wrapper::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background:
        radial-gradient(circle at 10% 20%, rgba(255, 215, 0, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(255, 165, 0, 0.03) 0%, transparent 50%) !important;
    pointer-events: none !important;
    z-index: -1 !important;
}

/* Content Areas */
.page-content {
    background: transparent !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Row and Column Styling */
.row {
    background: transparent !important;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    background: transparent !important;
}

/* Main Wrapper */
.main-wrapper {
    background: transparent !important;
    min-height: 100vh !important;
}

/* Auth Page Styling */
.auth-page {
    background: transparent !important;
}

.auth-left-wrapper-pic {
    background: var(--dark-gradient) !important;
    border-right: 2px solid var(--golden-accent) !important;
}

/* Content Sections */
.content-section {
    background: rgba(45, 45, 45, 0.8) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: var(--dark-shadow) !important;
}

/* Main content area with dark gold background */
.main-content-area, .content-main, .page-main {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 50%, #2d2d2d 100%) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 20px !important;
    border-radius: 8px !important;
    border: 1px solid var(--golden-accent) !important;
}

/* About section and similar content blocks */
.about-section, .info-section, .description-section {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 20px !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
}

/* Text content areas */
.text-content, .description, .info-text {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 15px !important;
    border-radius: 6px !important;
}

/* Feature lists and bullet points */
.feature-list, .info-list, ul.features {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 15px !important;
    border-left: 3px solid var(--golden-primary) !important;
    border-radius: 0 6px 6px 0 !important;
}

.feature-list li, .info-list li, ul.features li {
    color: var(--dark-text-primary) !important;
    margin-bottom: 8px !important;
}

/* Server info and status sections */
.server-info, .server-details, .game-info {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 20px !important;
    border: 2px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    margin: 15px 0 !important;
}

/* Welcome messages and announcements */
.welcome-message, .announcement, .news-content {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 20px !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    box-shadow: var(--dark-shadow) !important;
}

/* Any section with white or light background */
section, .section {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
}

/* Specific override for inline styles */
[style*="background-color:#ffffff"],
[style*="background-color: #ffffff"],
[style*="background-color:#fff"],
[style*="background-color: #fff"],
[style*="background-color:white"],
[style*="background-color: white"],
[style*="background:#ffffff"],
[style*="background: #ffffff"],
[style*="background:#fff"],
[style*="background: #fff"],
[style*="background:white"],
[style*="background: white"] {
    background: var(--dark-bg-secondary) !important;
    background-color: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
}

/* Well and Panel Styling */
.well {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.panel {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.panel-heading {
    background: var(--dark-gradient) !important;
    border-bottom: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

.panel-body {
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
}

/* Jumbotron */
.jumbotron {
    background: var(--dark-gradient) !important;
    border: 2px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
    box-shadow: var(--dark-shadow) !important;
}

/* Hero Section */
.hero-section {
    background: var(--dark-gradient) !important;
    border-bottom: 3px solid var(--golden-primary) !important;
    color: var(--dark-text-primary) !important;
    padding: 60px 0 !important;
    position: relative !important;
}

.hero-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background:
        radial-gradient(circle at 30% 70%, rgba(255, 215, 0, 0.1) 0%, transparent 50%) !important;
    pointer-events: none !important;
}

.hero-title {
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
    font-weight: bold !important;
}

.hero-subtitle {
    color: var(--dark-text-secondary) !important;
}

/* Section Dividers */
.section-divider {
    height: 2px !important;
    background: var(--golden-gradient) !important;
    margin: 40px 0 !important;
    border-radius: 1px !important;
}

/* Background Patterns */
.pattern-bg {
    background-image:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 10px,
            rgba(255, 215, 0, 0.02) 10px,
            rgba(255, 215, 0, 0.02) 20px
        ) !important;
}

/* Overlay Effects */
.overlay {
    background: rgba(26, 26, 26, 0.9) !important;
    backdrop-filter: blur(5px) !important;
}

.golden-overlay {
    background: rgba(255, 215, 0, 0.1) !important;
    backdrop-filter: blur(2px) !important;
}

/* Navigation Styling */
.horizontal-menu .navbar {
    background: var(--dark-gradient) !important;
    border-bottom: 2px solid var(--golden-primary) !important;
    box-shadow: var(--dark-shadow) !important;
}

.horizontal-menu .navbar .navbar-content .navbar-brand {
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
    font-weight: bold !important;
}

.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item .nav-link {
    color: var(--dark-text-secondary) !important;
    transition: all 0.3s ease !important;
}

.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item .nav-link:hover {
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
}

/* Bottom Navigation */
.horizontal-menu .bottom-navbar {
    background: var(--dark-bg-secondary) !important;
    border-bottom: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item > .nav-link {
    color: var(--dark-text-secondary) !important;
    transition: all 0.3s ease !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item > .nav-link:hover {
    color: var(--golden-primary) !important;
    background: rgba(255, 215, 0, 0.1) !important;
    text-shadow: var(--golden-glow) !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item.active > .nav-link {
    color: var(--golden-primary) !important;
    background: rgba(255, 215, 0, 0.15) !important;
    text-shadow: var(--golden-glow) !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item.active > .nav-link:before {
    background: var(--golden-gradient) !important;
}

/* Cards and Content Areas */
.card {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
    color: var(--dark-text-primary) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    border-color: var(--golden-primary) !important;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.2) !important;
    transform: translateY(-2px) !important;
}

.card-header {
    background: var(--dark-gradient) !important;
    border-bottom: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
    font-weight: bold !important;
    padding: 1rem 1.25rem !important;
}

.card-header h1, .card-header h2, .card-header h3,
.card-header h4, .card-header h5, .card-header h6 {
    color: var(--golden-primary) !important;
    margin-bottom: 0 !important;
}

.card-body {
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    padding: 1.25rem !important;
}

.card-footer {
    background: var(--dark-bg-tertiary) !important;
    border-top: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-secondary) !important;
    padding: 0.75rem 1.25rem !important;
}

/* Card Variants */
.card-primary {
    border-color: var(--golden-primary) !important;
}

.card-primary .card-header {
    background: var(--golden-gradient) !important;
    color: var(--dark-bg-primary) !important;
}

.card-secondary {
    border-color: var(--golden-accent) !important;
}

.card-success {
    border-color: #28a745 !important;
}

.card-info {
    border-color: #17a2b8 !important;
}

.card-warning {
    border-color: #ffc107 !important;
}

.card-danger {
    border-color: #dc3545 !important;
}

/* Card Groups */
.card-group .card {
    border-radius: 0 !important;
}

.card-group .card:first-child {
    border-top-left-radius: 8px !important;
    border-bottom-left-radius: 8px !important;
}

.card-group .card:last-child {
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
}

/* Card Deck */
.card-deck .card {
    margin-bottom: 15px !important;
}

/* Card Columns */
.card-columns .card {
    margin-bottom: 0.75rem !important;
}

/* Content Wrappers */
.content-wrapper {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
}

.content-header {
    background: var(--dark-gradient) !important;
    border-bottom: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
    padding: 15px 20px !important;
    margin: -20px -20px 20px -20px !important;
    font-weight: bold !important;
}

/* Widget Styling */
.widget {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: var(--dark-shadow) !important;
}

.widget-header {
    color: var(--golden-primary) !important;
    font-weight: bold !important;
    margin-bottom: 15px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid var(--golden-accent) !important;
}

.widget-body {
    color: var(--dark-text-primary) !important;
}

/* Info Boxes */
.info-box {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    display: flex !important;
    align-items: center !important;
}

.info-box-icon {
    background: var(--golden-gradient) !important;
    color: var(--dark-bg-primary) !important;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 15px !important;
    font-size: 1.5rem !important;
}

.info-box-content {
    flex: 1 !important;
}

.info-box-title {
    color: var(--golden-primary) !important;
    font-weight: bold !important;
    margin-bottom: 5px !important;
}

.info-box-text {
    color: var(--dark-text-primary) !important;
}

/* Small Boxes */
.small-box {
    background: var(--dark-gradient) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    position: relative !important;
    overflow: hidden !important;
}

.small-box::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 100px !important;
    height: 100px !important;
    background: rgba(255, 215, 0, 0.1) !important;
    border-radius: 50% !important;
    transform: translate(30px, -30px) !important;
}

.small-box-header {
    color: var(--golden-primary) !important;
    font-size: 2rem !important;
    font-weight: bold !important;
    margin-bottom: 5px !important;
}

.small-box-text {
    color: var(--dark-text-secondary) !important;
    text-transform: uppercase !important;
    font-size: 0.9rem !important;
}

/* Callouts */
.callout {
    background: var(--dark-bg-secondary) !important;
    border-left: 4px solid var(--golden-primary) !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    border-radius: 0 4px 4px 0 !important;
}

.callout-primary {
    border-left-color: var(--golden-primary) !important;
}

.callout-secondary {
    border-left-color: var(--golden-accent) !important;
}

.callout-success {
    border-left-color: #28a745 !important;
}

.callout-info {
    border-left-color: #17a2b8 !important;
}

.callout-warning {
    border-left-color: #ffc107 !important;
}

.callout-danger {
    border-left-color: #dc3545 !important;
}

/* Timeline */
.timeline {
    position: relative !important;
    padding-left: 30px !important;
}

.timeline::before {
    content: '' !important;
    position: absolute !important;
    left: 15px !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 2px !important;
    background: var(--golden-accent) !important;
}

.timeline-item {
    position: relative !important;
    margin-bottom: 30px !important;
}

.timeline-item::before {
    content: '' !important;
    position: absolute !important;
    left: -22px !important;
    top: 0 !important;
    width: 12px !important;
    height: 12px !important;
    background: var(--golden-primary) !important;
    border-radius: 50% !important;
    border: 2px solid var(--dark-bg-secondary) !important;
}

.timeline-content {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 15px !important;
}

.timeline-header {
    color: var(--golden-primary) !important;
    font-weight: bold !important;
    margin-bottom: 10px !important;
}

.timeline-body {
    color: var(--dark-text-primary) !important;
}

/* Buttons */
.btn-primary {
    background: var(--golden-gradient) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-bg-primary) !important;
    font-weight: bold !important;
    text-shadow: none !important;
    box-shadow: var(--golden-shadow) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    background: var(--golden-gradient-hover) !important;
    border: 1px solid var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
    box-shadow: var(--golden-glow) !important;
    transform: translateY(-2px) !important;
}

.btn-secondary {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
    transition: all 0.3s ease !important;
}

.btn-secondary:hover {
    background: var(--golden-accent) !important;
    color: var(--dark-bg-primary) !important;
    box-shadow: var(--golden-shadow) !important;
}

/* Forms and Inputs */
.form-control, input[type="text"], input[type="email"], input[type="password"],
input[type="number"], input[type="tel"], input[type="url"], input[type="search"],
textarea, select {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, input[type="text"]:focus, input[type="email"]:focus,
input[type="password"]:focus, input[type="number"]:focus, input[type="tel"]:focus,
input[type="url"]:focus, input[type="search"]:focus, textarea:focus, select:focus {
    background: var(--dark-bg-secondary) !important;
    border-color: var(--golden-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
    color: var(--dark-text-primary) !important;
    outline: none !important;
}

.form-control::placeholder, input::placeholder, textarea::placeholder {
    color: var(--dark-text-muted) !important;
    opacity: 0.8 !important;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem !important;
}

.form-group label {
    color: var(--golden-primary) !important;
    font-weight: 500 !important;
    margin-bottom: 0.5rem !important;
}

/* Input Groups */
.input-group {
    background: transparent !important;
}

.input-group-prepend .input-group-text,
.input-group-append .input-group-text {
    background: var(--golden-accent) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-bg-primary) !important;
    font-weight: bold !important;
}

.input-group-prepend .input-group-text:hover,
.input-group-append .input-group-text:hover {
    background: var(--golden-primary) !important;
    border-color: var(--golden-primary) !important;
}

/* Checkboxes and Radio Buttons */
.form-check-input {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.form-check-input:checked {
    background: var(--golden-gradient) !important;
    border-color: var(--golden-primary) !important;
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

.form-check-label {
    color: var(--dark-text-primary) !important;
}

/* Custom Select */
.custom-select {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.custom-select:focus {
    background: var(--dark-bg-secondary) !important;
    border-color: var(--golden-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

/* File Input */
.custom-file-input {
    background: var(--dark-bg-tertiary) !important;
}

.custom-file-label {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: var(--golden-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

/* Range Input */
.custom-range {
    background: transparent !important;
}

.custom-range::-webkit-slider-track {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.custom-range::-webkit-slider-thumb {
    background: var(--golden-gradient) !important;
    border: 1px solid var(--golden-primary) !important;
}

.custom-range::-moz-range-track {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.custom-range::-moz-range-thumb {
    background: var(--golden-gradient) !important;
    border: 1px solid var(--golden-primary) !important;
}

/* Form Validation */
.is-valid {
    border-color: #28a745 !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.valid-feedback {
    color: #28a745 !important;
}

.invalid-feedback {
    color: #dc3545 !important;
}

/* Form Text */
.form-text {
    color: var(--dark-text-muted) !important;
}

/* Fieldset and Legend */
fieldset {
    border: 1px solid var(--golden-accent) !important;
    border-radius: 4px !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
}

legend {
    color: var(--golden-primary) !important;
    font-weight: bold !important;
    padding: 0 10px !important;
    width: auto !important;
}

/* Switch/Toggle */
.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
    background: var(--golden-gradient) !important;
    border-color: var(--golden-primary) !important;
}

.custom-switch .custom-control-label::before {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
}

/* Form Row */
.form-row {
    background: transparent !important;
}

/* Input Sizing */
.form-control-sm, .custom-select-sm {
    font-size: 0.875rem !important;
    padding: 0.25rem 0.5rem !important;
}

.form-control-lg, .custom-select-lg {
    font-size: 1.125rem !important;
    padding: 0.5rem 1rem !important;
}

/* Disabled State */
.form-control:disabled, .custom-select:disabled {
    background: var(--dark-bg-primary) !important;
    border-color: var(--dark-text-muted) !important;
    color: var(--dark-text-muted) !important;
    opacity: 0.6 !important;
}

/* Form Control Plaintext */
.form-control-plaintext {
    background: transparent !important;
    border: none !important;
    color: var(--dark-text-primary) !important;
}

/* Input Group Sizing */
.input-group-sm .form-control,
.input-group-sm .custom-select,
.input-group-sm .input-group-text {
    font-size: 0.875rem !important;
    padding: 0.25rem 0.5rem !important;
}

.input-group-lg .form-control,
.input-group-lg .custom-select,
.input-group-lg .input-group-text {
    font-size: 1.125rem !important;
    padding: 0.5rem 1rem !important;
}

/* Tables */
.table {
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
}

.table th {
    background: var(--dark-gradient) !important;
    color: var(--golden-primary) !important;
    border-color: var(--golden-accent) !important;
}

.table td {
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    border-color: var(--golden-accent) !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(255, 215, 0, 0.05) !important;
}

.table-hover tbody tr:hover {
    background: rgba(255, 215, 0, 0.1) !important;
    color: var(--dark-text-primary) !important;
}

/* Links */
a {
    color: var(--golden-primary) !important;
    transition: all 0.3s ease !important;
}

a:hover {
    color: var(--golden-light) !important;
    text-decoration: none !important;
    text-shadow: var(--golden-glow) !important;
}

/* Text Colors */
h1, h2, h3, h4, h5, h6 {
    color: var(--golden-primary) !important;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3) !important;
}

p {
    color: var(--dark-text-primary) !important;
}

/* Dropdown Menus */
.dropdown-menu {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
}

.dropdown-item {
    color: var(--dark-text-primary) !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: rgba(255, 215, 0, 0.1) !important;
    color: var(--golden-primary) !important;
}

/* Badges and Labels */
.badge-primary {
    background: var(--golden-gradient) !important;
    color: var(--dark-bg-primary) !important;
}

.badge-secondary {
    background: var(--dark-bg-tertiary) !important;
    color: var(--golden-primary) !important;
    border: 1px solid var(--golden-accent) !important;
}

/* Alerts */
.alert-primary {
    background: rgba(255, 215, 0, 0.1) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

.alert-secondary {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

/* Modal */
.modal-content {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
}

.modal-header {
    background: var(--dark-gradient) !important;
    border-bottom: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

.modal-body {
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
}

.modal-footer {
    background: var(--dark-bg-secondary) !important;
    border-top: 1px solid var(--golden-accent) !important;
}

/* Pagination */
.page-link {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

.page-link:hover {
    background: var(--golden-accent) !important;
    color: var(--dark-bg-primary) !important;
}

.page-item.active .page-link {
    background: var(--golden-gradient) !important;
    border-color: var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
}

/* Progress Bars */
.progress {
    background: var(--dark-bg-tertiary) !important;
}

.progress-bar {
    background: var(--golden-gradient) !important;
}

/* List Groups */
.list-group-item {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.list-group-item:hover {
    background: rgba(255, 215, 0, 0.1) !important;
}

.list-group-item.active {
    background: var(--golden-gradient) !important;
    border-color: var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
}

/* Gaming Specific Elements */
.shop-item-detail {
    background: var(--dark-bg-secondary) !important;
    border: 2px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
    box-shadow: var(--golden-shadow) !important;
}

.shop-item-text {
    color: var(--golden-primary) !important;
    font-weight: bold !important;
}

/* Quality Colors for Items */
.shop-quality-0 { color: #888888 !important; }
.shop-quality-1 { color: #ffffff !important; }
.shop-quality-2 { color: #1eff00 !important; }
.shop-quality-3 { color: #0070dd !important; }
.shop-quality-4 { color: #a335ee !important; }
.shop-quality-5 { color: #ff8000 !important; }
.shop-quality-6 { color: #e6cc80 !important; }
.shop-quality-7 { color: #00ccff !important; }
.shop-quality-8 { color: #e5cc80 !important; }
.shop-quality-9 { color: #ff0040 !important; }

/* Card Headers for Shop */
.card-header-shop {
    background: var(--dark-gradient) !important;
    border-bottom: 2px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

.card-colored {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
}

/* Sidebar and Navigation Enhancements */
.sidebar {
    background: var(--dark-bg-secondary) !important;
    border-right: 2px solid var(--golden-accent) !important;
}

/* Top Navbar Enhancements */
.top-navbar {
    background: var(--dark-gradient) !important;
    border-bottom: 2px solid var(--golden-primary) !important;
    box-shadow: var(--dark-shadow) !important;
}

.navbar-brand img {
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)) !important;
}

/* Search Form */
.search-form .form-control {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.search-form .input-group-text {
    background: var(--golden-accent) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-bg-primary) !important;
}

/* Breadcrumbs */
.breadcrumb {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.breadcrumb-item a {
    color: var(--golden-primary) !important;
}

.breadcrumb-item.active {
    color: var(--dark-text-secondary) !important;
}

/* Tabs */
.nav-tabs {
    border-bottom: 2px solid var(--golden-accent) !important;
}

.nav-tabs .nav-link {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-secondary) !important;
    transition: all 0.3s ease !important;
}

.nav-tabs .nav-link:hover {
    background: rgba(255, 215, 0, 0.1) !important;
    color: var(--golden-primary) !important;
}

.nav-tabs .nav-link.active {
    background: var(--golden-gradient) !important;
    border-color: var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
}

/* Pills */
.nav-pills .nav-link {
    background: var(--dark-bg-tertiary) !important;
    color: var(--dark-text-secondary) !important;
    transition: all 0.3s ease !important;
}

.nav-pills .nav-link:hover {
    background: rgba(255, 215, 0, 0.1) !important;
    color: var(--golden-primary) !important;
}

.nav-pills .nav-link.active {
    background: var(--golden-gradient) !important;
    color: var(--dark-bg-primary) !important;
}

/* Tooltips */
.tooltip-inner {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

/* Popovers */
.popover {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.popover-header {
    background: var(--dark-gradient) !important;
    border-bottom: 1px solid var(--golden-accent) !important;
    color: var(--golden-primary) !important;
}

.popover-body {
    color: var(--dark-text-primary) !important;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 12px;
    background: var(--dark-bg-tertiary) !important;
}

::-webkit-scrollbar-thumb {
    background: var(--golden-gradient) !important;
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--golden-gradient-hover) !important;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg-secondary) !important;
}

/* Selection */
::selection {
    background: var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
}

::-moz-selection {
    background: var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
}

/* Focus Outline */
*:focus {
    outline: 2px solid var(--golden-primary) !important;
    outline-offset: 2px !important;
}

/* Glow Effects for Interactive Elements */
.btn:hover,
.card:hover,
.nav-link:hover {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3) !important;
}

/* Special Gaming UI Elements */
.stat-card {
    background: var(--dark-gradient) !important;
    border: 2px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

.stat-card:hover {
    border-color: var(--golden-primary) !important;
    box-shadow: var(--golden-glow) !important;
    transform: translateY(-5px) !important;
}

.stat-value {
    font-size: 2rem !important;
    font-weight: bold !important;
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
}

.stat-label {
    color: var(--dark-text-secondary) !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

/* Top Navbar Specific Styling */
.top-navbar .navbar-brand {
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
    font-weight: bold !important;
}

.top-navbar .navbar-nav .nav-item .nav-link {
    color: var(--dark-text-secondary) !important;
    transition: all 0.3s ease !important;
    padding: 10px 15px !important;
}

.top-navbar .navbar-nav .nav-item .nav-link:hover {
    color: var(--golden-primary) !important;
    background: rgba(255, 215, 0, 0.1) !important;
    text-shadow: var(--golden-glow) !important;
    border-radius: 5px !important;
}

.top-navbar .navbar-nav .nav-item.dropdown .dropdown-menu {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
}

.top-navbar .navbar-nav .nav-item.dropdown .dropdown-item {
    color: var(--dark-text-primary) !important;
    transition: all 0.3s ease !important;
}

.top-navbar .navbar-nav .nav-item.dropdown .dropdown-item:hover {
    background: rgba(255, 215, 0, 0.1) !important;
    color: var(--golden-primary) !important;
}

/* Language Dropdown Styling */
.flag-icon {
    filter: brightness(1.2) !important;
}

/* Navbar Toggler */
.navbar-toggler {
    border-color: var(--golden-accent) !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

/* Page Content Wrapper */
.page-wrapper {
    background: var(--dark-bg-primary) !important;
    min-height: 100vh !important;
}

.page-content {
    background: transparent !important;
}

/* Main Content Area */
.main-wrapper {
    background: var(--dark-bg-primary) !important;
}

/* Footer Styling */
footer {
    background: var(--dark-gradient) !important;
    border-top: 2px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

/* Horizontal Menu Enhancements */
.horizontal-menu {
    background: var(--dark-bg-primary) !important;
}

/* Submenu Styling */
.horizontal-menu .bottom-navbar .page-navigation > .nav-item .submenu {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item .submenu ul li a {
    color: var(--dark-text-primary) !important;
    transition: all 0.3s ease !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item .submenu ul li a:hover {
    color: var(--golden-primary) !important;
    background: rgba(255, 215, 0, 0.1) !important;
    padding-left: 25px !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item .submenu ul li a.active {
    color: var(--golden-primary) !important;
    background: rgba(255, 215, 0, 0.15) !important;
}

/* Menu Icons */
.horizontal-menu .bottom-navbar .page-navigation > .nav-item > .nav-link .link-icon {
    color: inherit !important;
    transition: all 0.3s ease !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item:hover > .nav-link .link-icon {
    color: var(--golden-primary) !important;
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5)) !important;
}

/* Menu Arrows */
.horizontal-menu .bottom-navbar .page-navigation > .nav-item > .nav-link .link-arrow {
    color: inherit !important;
    transition: all 0.3s ease !important;
}

.horizontal-menu .bottom-navbar .page-navigation > .nav-item:hover > .nav-link .link-arrow {
    color: var(--golden-primary) !important;
}

/* Active Menu Item Enhancements */
.horizontal-menu .bottom-navbar .page-navigation > .nav-item.active > .nav-link .link-icon {
    color: var(--golden-primary) !important;
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5)) !important;
}

/* Responsive Menu */
@media (max-width: 991.98px) {
    .horizontal-menu .bottom-navbar {
        background: var(--dark-bg-secondary) !important;
    }

    .horizontal-menu .bottom-navbar .page-navigation > .nav-item {
        border-bottom: 1px solid var(--golden-accent) !important;
    }

    .horizontal-menu .bottom-navbar .page-navigation > .nav-item:last-child {
        border-bottom: none !important;
    }
}

/* Search Form in Navbar */
.horizontal-menu .navbar .navbar-content .search-form .input-group .form-control {
    background: var(--dark-bg-tertiary) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.horizontal-menu .navbar .navbar-content .search-form .input-group .form-control:focus {
    background: var(--dark-bg-secondary) !important;
    border-color: var(--golden-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

.horizontal-menu .navbar .navbar-content .search-form .input-group .input-group-prepend .input-group-text {
    background: var(--golden-accent) !important;
    border: 1px solid var(--golden-accent) !important;
    color: var(--dark-bg-primary) !important;
}

/* Profile Dropdown */
.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item.nav-profile .dropdown-menu {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow) !important;
}

.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .info .name {
    color: var(--golden-primary) !important;
}

.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .info .email {
    color: var(--dark-text-secondary) !important;
}

.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-body .profile-nav .nav-item .nav-link {
    color: var(--dark-text-primary) !important;
    transition: all 0.3s ease !important;
}

.horizontal-menu .navbar .navbar-content .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-body .profile-nav .nav-item:hover .nav-link {
    color: var(--golden-primary) !important;
    background: rgba(255, 215, 0, 0.1) !important;
}

/* Shop and Webshop Specific Styling */
.shop-container {
    background: var(--dark-bg-primary) !important;
}

.shop-container .shop-header {
    background: var(--dark-gradient) !important;
    border: 2px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.shop-container .shop-header a {
    color: var(--golden-primary) !important;
}

.shop-container .shop-header a:hover,
.shop-container .shop-header a:active {
    color: var(--golden-light) !important;
    text-shadow: var(--golden-glow) !important;
}

.shop-container .shop-content {
    background: var(--dark-bg-secondary) !important;
    border: 2px solid var(--golden-accent) !important;
    color: var(--dark-text-primary) !important;
}

.shop-container .sidebar {
    background: var(--dark-bg-tertiary) !important;
    border-right: 1px solid var(--golden-accent) !important;
}

.webshop-container {
    background: var(--dark-bg-secondary) !important;
    border: 2px solid var(--golden-accent) !important;
    box-shadow: var(--dark-shadow), inset 0px 0px 62px 14px rgba(255, 215, 0, 0.1) !important;
    color: var(--dark-text-primary) !important;
}

.webshop-container a {
    color: var(--golden-primary) !important;
}

.webshop-container a:hover {
    color: var(--golden-light) !important;
    text-shadow: var(--golden-glow) !important;
}

.webshop-container .cselect-container {
    background: var(--dark-gradient) !important;
    box-shadow: inset 0px 0px 62px 14px rgba(255, 215, 0, 0.1) !important;
}

.webshop-container .cselect-container span {
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
}

/* Item Quality Colors Enhancement */
.shop-quality-0 {
    color: #888888 !important;
    text-shadow: 0 0 5px rgba(136, 136, 136, 0.5) !important;
}
.shop-quality-1 {
    color: #ffffff !important;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5) !important;
}
.shop-quality-2 {
    color: #1eff00 !important;
    text-shadow: 0 0 5px rgba(30, 255, 0, 0.5) !important;
}
.shop-quality-3 {
    color: #0070dd !important;
    text-shadow: 0 0 5px rgba(0, 112, 221, 0.5) !important;
}
.shop-quality-4 {
    color: #a335ee !important;
    text-shadow: 0 0 5px rgba(163, 53, 238, 0.5) !important;
}
.shop-quality-5 {
    color: #ff8000 !important;
    text-shadow: 0 0 5px rgba(255, 128, 0, 0.5) !important;
}
.shop-quality-6 {
    color: #e6cc80 !important;
    text-shadow: 0 0 5px rgba(230, 204, 128, 0.5) !important;
}
.shop-quality-7 {
    color: #00ccff !important;
    text-shadow: 0 0 5px rgba(0, 204, 255, 0.5) !important;
}
.shop-quality-8 {
    color: #e5cc80 !important;
    text-shadow: 0 0 5px rgba(229, 204, 128, 0.5) !important;
}
.shop-quality-9 {
    color: #ff0040 !important;
    text-shadow: 0 0 5px rgba(255, 0, 64, 0.5) !important;
}

/* Rankings and Profile Enhancements */
.rankings-table {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
}

.rankings-table th {
    background: var(--dark-gradient) !important;
    color: var(--golden-primary) !important;
    border-color: var(--golden-accent) !important;
}

.rankings-table td {
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
    border-color: var(--golden-accent) !important;
}

.rankings-table tr:hover {
    background: rgba(255, 215, 0, 0.1) !important;
}

/* Profile Cards */
.profile-card {
    background: var(--dark-bg-secondary) !important;
    border: 2px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    box-shadow: var(--dark-shadow) !important;
}

.profile-header {
    background: var(--dark-gradient) !important;
    color: var(--golden-primary) !important;
    padding: 20px !important;
    text-align: center !important;
}

.profile-avatar {
    border: 3px solid var(--golden-primary) !important;
    border-radius: 50% !important;
    box-shadow: var(--golden-glow) !important;
}

.profile-name {
    color: var(--golden-primary) !important;
    font-weight: bold !important;
    font-size: 1.5rem !important;
    text-shadow: var(--golden-glow) !important;
}

.profile-level {
    color: var(--dark-text-secondary) !important;
    font-size: 1rem !important;
}

/* Lottery and Gaming Elements */
.lottery-wheel {
    border: 3px solid var(--golden-primary) !important;
    box-shadow: var(--golden-glow) !important;
}

.lottery-button {
    background: var(--golden-gradient) !important;
    border: 2px solid var(--golden-primary) !important;
    color: var(--dark-bg-primary) !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    padding: 15px 30px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.lottery-button:hover {
    background: var(--golden-gradient-hover) !important;
    box-shadow: var(--golden-glow) !important;
    transform: translateY(-2px) !important;
}

/* Server Status */
.server-status {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    padding: 15px !important;
    text-align: center !important;
}

.server-online {
    color: #28a745 !important;
    font-weight: bold !important;
    text-shadow: 0 0 5px rgba(40, 167, 69, 0.5) !important;
}

.server-offline {
    color: #dc3545 !important;
    font-weight: bold !important;
    text-shadow: 0 0 5px rgba(220, 53, 69, 0.5) !important;
}

/* News and Announcements */
.news-item {
    background: var(--dark-bg-secondary) !important;
    border: 1px solid var(--golden-accent) !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.news-item:hover {
    border-color: var(--golden-primary) !important;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.2) !important;
}

.news-header {
    background: var(--dark-gradient) !important;
    color: var(--golden-primary) !important;
    padding: 15px 20px !important;
    font-weight: bold !important;
}

.news-date {
    color: var(--dark-text-secondary) !important;
    font-size: 0.9rem !important;
    float: right !important;
}

.news-content {
    padding: 20px !important;
    color: var(--dark-text-primary) !important;
}

/* Event Styling */
.event-banner {
    background: var(--golden-gradient) !important;
    color: var(--dark-bg-primary) !important;
    padding: 20px !important;
    text-align: center !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
    box-shadow: var(--golden-shadow) !important;
}

.event-timer {
    background: var(--dark-bg-secondary) !important;
    border: 2px solid var(--golden-primary) !important;
    border-radius: 8px !important;
    padding: 15px !important;
    text-align: center !important;
    font-family: 'Courier New', monospace !important;
    font-size: 1.2rem !important;
    color: var(--golden-primary) !important;
    text-shadow: var(--golden-glow) !important;
}
